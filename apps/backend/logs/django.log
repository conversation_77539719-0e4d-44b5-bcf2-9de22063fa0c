Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 10846
Watching for file changes with StatReloader
"POST /api/auth/login/ HTTP/1.1" 200 950
"GET /api/clients/ HTTP/1.1" 200 52
"POST /api/clients/ HTTP/1.1" 201 240
"GET /api/projects/ HTTP/1.1" 200 52
"GET /api/tasks/ HTTP/1.1" 200 52
Watching for file changes with StatReloader
Unauthorized: /api/auth/logout/
"POST /api/auth/logout/ HTTP/1.1" 401 62
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"POST /api/auth/login/ HTTP/1.1" 200 950
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
Internal Server Error: /api/auth/profile/
Traceback (most recent call last):
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/views/decorators/csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/views/generic/base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/decorators.py", line 50, in handler
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/authentication/views.py", line 55, in user_profile
    return Response(serializer.data)
                    ^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 555, in data
    ret = super().data
          ^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 253, in data
    self._data = self.to_representation(self.instance)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 507, in to_representation
    for field in fields:
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 368, in _readable_fields
    for field in self.fields.values():
                 ^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 357, in fields
    fields[key] = value
    ~~~~~~^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/utils/serializer_helpers.py", line 169, in __setitem__
    field.bind(field_name=key, parent=self.serializer)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/fields.py", line 367, in bind
    assert self.source != field_name, (
           ^^^^^^^^^^^^^^^^^^^^^^^^^
AssertionError: It is redundant to specify `source='full_name'` on field 'CharField' in serializer 'UserProfileSerializer', because it is the same as the field name. Remove the `source` keyword argument.
"GET /api/auth/profile/ HTTP/1.1" 500 138384
"POST /api/auth/login/ HTTP/1.1" 200 950
"POST /api/clients/ HTTP/1.1" 201 237
"POST /api/auth/login/ HTTP/1.1" 200 950
"POST /api/clients/ HTTP/1.1" 201 226
"POST /api/auth/login/ HTTP/1.1" 200 950
Bad Request: /api/projects/
"POST /api/projects/ HTTP/1.1" 400 42
"POST /api/auth/login/ HTTP/1.1" 200 950
Internal Server Error: /api/projects/
Traceback (most recent call last):
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.IntegrityError: NOT NULL constraint failed: projects_project.client_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/views/decorators/csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/mixins.py", line 19, in create
    self.perform_create(serializer)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/projects/views.py", line 61, in perform_create
    serializer.save(project_manager=self.request.user)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 212, in save
    self.instance = self.create(validated_data)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/projects/serializers.py", line 98, in create
    project = Project.objects.create(**validated_data)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/query.py", line 658, in create
    obj.save(force_insert=True, using=self.db)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/base.py", line 814, in save
    self.save_base(
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/base.py", line 877, in save_base
    updated = self._save_table(
              ^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/base.py", line 1020, in _save_table
    results = self._do_insert(
              ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/base.py", line 1061, in _do_insert
    return manager._insert(
           ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/query.py", line 1805, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/sql/compiler.py", line 1822, in execute_sql
    cursor.execute(sql, params)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.IntegrityError: NOT NULL constraint failed: projects_project.client_id
"POST /api/projects/ HTTP/1.1" 500 226516
"POST /api/auth/login/ HTTP/1.1" 200 950
Internal Server Error: /api/clients/
Traceback (most recent call last):
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/views/decorators/csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/mixins.py", line 43, in list
    return self.get_paginated_response(serializer.data)
                                       ^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 768, in data
    ret = super().data
          ^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 253, in data
    self._data = self.to_representation(self.instance)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 686, in to_representation
    return [
           ^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 687, in <listcomp>
    self.child.to_representation(item) for item in iterable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 507, in to_representation
    for field in fields:
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 368, in _readable_fields
    for field in self.fields.values():
                 ^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 357, in fields
    fields[key] = value
    ~~~~~~^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/utils/serializer_helpers.py", line 169, in __setitem__
    field.bind(field_name=key, parent=self.serializer)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/fields.py", line 367, in bind
    assert self.source != field_name, (
           ^^^^^^^^^^^^^^^^^^^^^^^^^
AssertionError: It is redundant to specify `source='mood_emoji'` on field 'CharField' in serializer 'ClientListSerializer', because it is the same as the field name. Remove the `source` keyword argument.
"GET /api/clients/ HTTP/1.1" 500 146227
/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/clients/serializers.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/clients/serializers.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/clients/serializers.py changed, reloading.
Watching for file changes with StatReloader
"POST /api/auth/login/ HTTP/1.1" 200 950
"GET /api/clients/ HTTP/1.1" 200 1193
"POST /api/auth/login/ HTTP/1.1" 200 950
"GET /api/projects/ HTTP/1.1" 200 52
"POST /api/auth/login/ HTTP/1.1" 200 950
Internal Server Error: /api/projects/
Traceback (most recent call last):
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.IntegrityError: NOT NULL constraint failed: projects_project.client_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/views/decorators/csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/mixins.py", line 19, in create
    self.perform_create(serializer)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/projects/views.py", line 61, in perform_create
    serializer.save(project_manager=self.request.user)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 212, in save
    self.instance = self.create(validated_data)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/projects/serializers.py", line 98, in create
    project = Project.objects.create(**validated_data)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/query.py", line 658, in create
    obj.save(force_insert=True, using=self.db)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/base.py", line 814, in save
    self.save_base(
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/base.py", line 877, in save_base
    updated = self._save_table(
              ^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/base.py", line 1020, in _save_table
    results = self._do_insert(
              ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/base.py", line 1061, in _do_insert
    return manager._insert(
           ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/query.py", line 1805, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/sql/compiler.py", line 1822, in execute_sql
    cursor.execute(sql, params)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.IntegrityError: NOT NULL constraint failed: projects_project.client_id
"POST /api/projects/ HTTP/1.1" 500 226516
"POST /api/auth/login/ HTTP/1.1" 200 950
Internal Server Error: /api/projects/
Traceback (most recent call last):
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.IntegrityError: NOT NULL constraint failed: projects_project.client_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/views/decorators/csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/mixins.py", line 19, in create
    self.perform_create(serializer)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/projects/views.py", line 61, in perform_create
    serializer.save(project_manager=self.request.user)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 212, in save
    self.instance = self.create(validated_data)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/projects/serializers.py", line 98, in create
    project = Project.objects.create(**validated_data)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/query.py", line 658, in create
    obj.save(force_insert=True, using=self.db)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/base.py", line 814, in save
    self.save_base(
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/base.py", line 877, in save_base
    updated = self._save_table(
              ^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/base.py", line 1020, in _save_table
    results = self._do_insert(
              ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/base.py", line 1061, in _do_insert
    return manager._insert(
           ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/query.py", line 1805, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/sql/compiler.py", line 1822, in execute_sql
    cursor.execute(sql, params)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.IntegrityError: NOT NULL constraint failed: projects_project.client_id
"POST /api/projects/ HTTP/1.1" 500 226516
/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/projects/serializers.py changed, reloading.
Watching for file changes with StatReloader
"POST /api/auth/login/ HTTP/1.1" 200 950
"POST /api/projects/ HTTP/1.1" 201 417
"POST /api/auth/login/ HTTP/1.1" 200 950
Bad Request: /api/tasks/
"POST /api/tasks/ HTTP/1.1" 400 85
"POST /api/auth/login/ HTTP/1.1" 200 950
"POST /api/tasks/ HTTP/1.1" 201 352
/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/tasks/serializers.py changed, reloading.
Watching for file changes with StatReloader
"POST /api/auth/login/ HTTP/1.1" 200 950
"POST /api/tasks/ HTTP/1.1" 201 340
"POST /api/auth/login/ HTTP/1.1" 200 950
"OPTIONS /api/clients/ HTTP/1.1" 200 0
"GET /api/clients/ HTTP/1.1" 200 1193
"OPTIONS /api/projects/ HTTP/1.1" 200 0
"GET /api/projects/ HTTP/1.1" 200 549
"OPTIONS /api/tasks/ HTTP/1.1" 200 0
"GET /api/tasks/ HTTP/1.1" 200 947
Internal Server Error: /api/auth/profile/
Traceback (most recent call last):
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/views/decorators/csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/views/generic/base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/decorators.py", line 50, in handler
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/authentication/views.py", line 55, in user_profile
    return Response(serializer.data)
                    ^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 555, in data
    ret = super().data
          ^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 253, in data
    self._data = self.to_representation(self.instance)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 507, in to_representation
    for field in fields:
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 368, in _readable_fields
    for field in self.fields.values():
                 ^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 357, in fields
    fields[key] = value
    ~~~~~~^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/utils/serializer_helpers.py", line 169, in __setitem__
    field.bind(field_name=key, parent=self.serializer)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/fields.py", line 367, in bind
    assert self.source != field_name, (
           ^^^^^^^^^^^^^^^^^^^^^^^^^
AssertionError: It is redundant to specify `source='full_name'` on field 'CharField' in serializer 'UserProfileSerializer', because it is the same as the field name. Remove the `source` keyword argument.
"GET /api/auth/profile/ HTTP/1.1" 500 138384
"POST /api/auth/login/ HTTP/1.1" 200 950
Internal Server Error: /api/auth/profile/
Traceback (most recent call last):
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/views/decorators/csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/views/generic/base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/decorators.py", line 50, in handler
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/authentication/views.py", line 55, in user_profile
    return Response(serializer.data)
                    ^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 555, in data
    ret = super().data
          ^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 253, in data
    self._data = self.to_representation(self.instance)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 507, in to_representation
    for field in fields:
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 368, in _readable_fields
    for field in self.fields.values():
                 ^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 357, in fields
    fields[key] = value
    ~~~~~~^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/utils/serializer_helpers.py", line 169, in __setitem__
    field.bind(field_name=key, parent=self.serializer)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/fields.py", line 367, in bind
    assert self.source != field_name, (
           ^^^^^^^^^^^^^^^^^^^^^^^^^
AssertionError: It is redundant to specify `source='full_name'` on field 'CharField' in serializer 'UserProfileSerializer', because it is the same as the field name. Remove the `source` keyword argument.
"GET /api/auth/profile/ HTTP/1.1" 500 138384
"POST /api/auth/login/ HTTP/1.1" 200 950
Watching for file changes with StatReloader
Unauthorized: /api/clients/
"GET /api/clients/ HTTP/1.1" 401 286
Unauthorized: /api/projects/
"GET /api/projects/ HTTP/1.1" 401 286
Unauthorized: /api/clients/
"GET /api/clients/ HTTP/1.1" 401 286
Unauthorized: /api/clients/
"GET /api/clients/ HTTP/1.1" 401 286
Unauthorized: /api/projects/
"GET /api/projects/ HTTP/1.1" 401 286
Unauthorized: /api/tasks/
"GET /api/tasks/ HTTP/1.1" 401 286
Unauthorized: /api/tasks/
"GET /api/tasks/ HTTP/1.1" 401 286
Unauthorized: /api/projects/
"GET /api/projects/ HTTP/1.1" 401 286
Unauthorized: /api/clients/
"GET /api/clients/ HTTP/1.1" 401 286
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"OPTIONS /api/clients/ HTTP/1.1" 200 0
"OPTIONS /api/clients/ HTTP/1.1" 200 0
"OPTIONS /api/projects/ HTTP/1.1" 200 0
"OPTIONS /api/clients/ HTTP/1.1" 200 0
"OPTIONS /api/tasks/ HTTP/1.1" 200 0
"OPTIONS /api/clients/ HTTP/1.1" 200 0
"OPTIONS /api/projects/ HTTP/1.1" 200 0
"OPTIONS /api/tasks/ HTTP/1.1" 200 0
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/login/?next=/admin/ HTTP/1.1" 200 4350
"GET /static/admin/css/rtl.css HTTP/1.1" 200 4918
"GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2694
"GET /static/admin/css/login.css HTTP/1.1" 200 958
"GET /static/admin/css/base.css HTTP/1.1" 200 21310
"GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2929
"GET /static/admin/css/responsive_rtl.css HTTP/1.1" 200 1864
"GET /static/admin/js/theme.js HTTP/1.1" 200 1943
"GET /static/admin/css/responsive.css HTTP/1.1" 200 18559
"GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 2661
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"OPTIONS /api/clients/ HTTP/1.1" 200 0
"OPTIONS /api/projects/ HTTP/1.1" 200 0
"OPTIONS /api/tasks/ HTTP/1.1" 200 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"OPTIONS /api/projects/ HTTP/1.1" 200 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
i/clients/ HTTP/1.1" 200 1193
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/mtbrmg_erp/settings.py changed, reloading.
/app/mtbrmg_erp/settings.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/login/?next=/admin/ HTTP/1.1" 200 4350
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 2661
"POST /admin/login/?next=/admin/ HTTP/1.1" 200 4599
"GET /static/admin/css/base.css HTTP/1.1" 304 0
"GET /static/admin/css/dark_mode.css HTTP/1.1" 304 0
"GET /static/admin/css/responsive_rtl.css HTTP/1.1" 304 0
"GET /static/admin/css/login.css HTTP/1.1" 304 0
"GET /static/admin/css/nav_sidebar.css HTTP/1.1" 304 0
"GET /static/admin/js/nav_sidebar.js HTTP/1.1" 304 0
"GET /static/admin/js/theme.js HTTP/1.1" 304 0
"GET /static/admin/css/rtl.css HTTP/1.1" 304 0
"GET /static/admin/css/responsive.css HTTP/1.1" 304 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"POST /api/auth/login/ HTTP/1.1" 200 950
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
"POST /api/auth/login/ HTTP/1.1" 200 950
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"OPTIONS /api/clients/ HTTP/1.1" 200 0
Unauthorized: /api/clients/
"GET /api/clients/ HTTP/1.1" 401 286
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
Unauthorized: /api/clients/
"GET /api/clients/ HTTP/1.1" 401 286
"OPTIONS /api/projects/ HTTP/1.1" 200 0
Unauthorized: /api/projects/
"GET /api/projects/ HTTP/1.1" 401 286
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
Unauthorized: /api/clients/
"GET /api/clients/ HTTP/1.1" 401 286
"GET /admin/ HTTP/1.1" 302 0
Unauthorized: /api/auth/logout/
"POST /api/auth/logout/ HTTP/1.1" 401 62
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"POST /api/auth/login/ HTTP/1.1" 200 950
"GET /api/projects/ HTTP/1.1" 200 52
"GET /admin/ HTTP/1.1" 302 0
"OPTIONS /api/tasks/ HTTP/1.1" 200 0
"GET /api/tasks/ HTTP/1.1" 200 52
"GET /api/projects/ HTTP/1.1" 200 52
"OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
"POST /api/auth/logout/ HTTP/1.1" 200 53
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
Watching for file changes with StatReloader
"POST /api/auth/login/ HTTP/1.1" 200 1089
"GET /api/clients/ HTTP/1.1" 200 1193
Watching for file changes with StatReloader
"POST /api/auth/login/ HTTP/1.1" 200 1089
"GET /api/clients/ HTTP/1.1" 200 1193
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"POST /api/auth/login/ HTTP/1.1" 200 1089
"GET /api/clients/ HTTP/1.1" 200 1193
Bad Request: /api/clients/
"POST /api/clients/ HTTP/1.1" 400 75
"OPTIONS /api/clients/3/ HTTP/1.1" 200 0
"PATCH /api/clients/3/ HTTP/1.1" 200 778
"GET /api/projects/ HTTP/1.1" 200 549
"GET /api/clients/ HTTP/1.1" 200 1195
"POST /api/clients/ HTTP/1.1" 201 281
Internal Server Error: /api/auth/profile/
Traceback (most recent call last):
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/views/decorators/csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/views/generic/base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/decorators.py", line 50, in handler
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/authentication/views.py", line 55, in user_profile
    return Response(serializer.data)
                    ^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/serializers.py", line 555, in data
    ret = super().data
          ^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/serializers.py", line 253, in data
    self._data = self.to_representation(self.instance)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/serializers.py", line 507, in to_representation
    for field in fields:
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/serializers.py", line 368, in _readable_fields
    for field in self.fields.values():
                 ^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/serializers.py", line 357, in fields
    fields[key] = value
    ~~~~~~^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/utils/serializer_helpers.py", line 169, in __setitem__
    field.bind(field_name=key, parent=self.serializer)
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/fields.py", line 367, in bind
    assert self.source != field_name, (
AssertionError: It is redundant to specify `source='full_name'` on field 'CharField' in serializer 'UserProfileSerializer', because it is the same as the field name. Remove the `source` keyword argument.
"GET /api/auth/profile/ HTTP/1.1" 500 142633
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/authentication/serializers.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/authentication/serializers.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/auth/profile/ HTTP/1.1" 200 363
"POST /api/auth/login/ HTTP/1.1" 200 1089
"POST /api/clients/ HTTP/1.1" 201 273
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/clients/ HTTP/1.1" 200 1942
"GET /api/auth/profile/ HTTP/1.1" 200 363
Bad Request: /api/clients/
"POST /api/clients/ HTTP/1.1" 400 75
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/clients/ HTTP/1.1" 200 1942
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
"POST /api/auth/logout/ HTTP/1.1" 200 53
"POST /api/auth/login/ HTTP/1.1" 200 1089
"GET /api/clients/ HTTP/1.1" 200 1942
Bad Request: /api/clients/
"POST /api/clients/ HTTP/1.1" 400 75
"GET /api/auth/profile/ HTTP/1.1" 200 363
"POST /api/auth/login/ HTTP/1.1" 200 950
"GET /api/projects/ HTTP/1.1" 200 549
"GET /api/tasks/ HTTP/1.1" 200 947
"GET /api/projects/ HTTP/1.1" 200 549
Bad Request: /api/projects/
"POST /api/projects/ HTTP/1.1" 400 142
"POST /api/auth/logout/ HTTP/1.1" 200 53
"POST /api/auth/login/ HTTP/1.1" 200 1089
Bad Request: /api/clients/
"POST /api/clients/ HTTP/1.1" 400 75
"POST /api/clients/ HTTP/1.1" 201 194
Bad Request: /api/clients/
"POST /api/clients/ HTTP/1.1" 400 72
Bad Request: /api/clients/
"POST /api/clients/ HTTP/1.1" 400 75
Bad Request: /api/clients/
"POST /api/clients/ HTTP/1.1" 400 101
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"POST /api/auth/logout/ HTTP/1.1" 200 53
Unauthorized: /api/auth/logout/
"POST /api/auth/logout/ HTTP/1.1" 401 62
Unauthorized: /api/auth/logout/
"POST /api/auth/logout/ HTTP/1.1" 401 62
Unauthorized: /api/auth/logout/
"POST /api/auth/logout/ HTTP/1.1" 401 62
Unauthorized: /api/auth/logout/
"POST /api/auth/logout/ HTTP/1.1" 401 62
Unauthorized: /api/auth/logout/
"POST /api/auth/logout/ HTTP/1.1" 401 62
Unauthorized: /api/auth/logout/
"POST /api/auth/logout/ HTTP/1.1" 401 62
Unauthorized: /api/
"GET /api/ HTTP/1.1" 401 62
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"POST /api/auth/login/ HTTP/1.1" 200 1089
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"POST /api/auth/login/ HTTP/1.1" 200 1089
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"POST /api/auth/login/ HTTP/1.1" 200 1089
"POST /api/auth/login/ HTTP/1.1" 200 1089
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"POST /api/auth/login/ HTTP/1.1" 200 1089
"OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
"POST /api/auth/logout/ HTTP/1.1" 200 53
"POST /api/auth/login/ HTTP/1.1" 200 1089
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
Watching for file changes with StatReloader
ET /api/auth/profile/ HTTP/1.1" 200 363
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
/app/team/models.py changed, reloading.
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/team/models.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
/app/mtbrmg_erp/urls.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
/app/team/admin.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
Unauthorized: /api/team/
"GET /api/team/ HTTP/1.1" 401 62
Unauthorized: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 401 92
"GET /admin/ HTTP/1.1" 302 0
"POST /api/auth/login/ HTTP/1.1" 200 1078
"POST /api/auth/login/ HTTP/1.1" 200 1078
"GET /api/team/ HTTP/1.1" 200 52
"POST /api/auth/login/ HTTP/1.1" 200 1078
"GET /api/team/stats/ HTTP/1.1" 200 218
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"POST /api/auth/login/ HTTP/1.1" 200 1078
"GET /api/team/ HTTP/1.1" 200 2432
"POST /api/auth/login/ HTTP/1.1" 200 1078
"GET /api/team/stats/ HTTP/1.1" 200 709
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
Unauthorized: /api/auth/profile/
"GET /api/auth/profile/ HTTP/1.1" 401 286
Unauthorized: /api/auth/profile/
"GET /api/auth/profile/ HTTP/1.1" 401 286
"OPTIONS /api/auth/refresh/ HTTP/1.1" 200 0
Unauthorized: /api/auth/refresh/
"POST /api/auth/refresh/ HTTP/1.1" 401 116
Unauthorized: /api/auth/refresh/
"POST /api/auth/refresh/ HTTP/1.1" 401 116
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"POST /api/auth/login/ HTTP/1.1" 200 1078
"GET /api/team/stats/ HTTP/1.1" 200 709
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"POST /api/auth/login/ HTTP/1.1" 200 1078
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
Watching for file changes with StatReloader
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /admin/ HTTP/1.1" 302 0
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /admin/ HTTP/1.1" 302 0
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /admin/ HTTP/1.1" 302 0
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /admin/ HTTP/1.1" 302 0
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /admin/ HTTP/1.1" 302 0
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
"POST /api/auth/logout/ HTTP/1.1" 200 53
"GET /admin/ HTTP/1.1" 302 0
"POST /api/auth/login/ HTTP/1.1" 200 1078
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /admin/ HTTP/1.1" 302 0
Unauthorized: /api/projects/
"GET /api/projects/ HTTP/1.1" 401 62
Unauthorized: /api/projects/
"POST /api/projects/ HTTP/1.1" 401 62
"GET /admin/ HTTP/1.1" 302 0
Unauthorized: /api/projects/
"GET /api/projects/ HTTP/1.1" 401 62
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /admin/ HTTP/1.1" 302 0
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"POST /api/auth/login/ HTTP/1.1" 200 1078
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /admin/ HTTP/1.1" 302 0
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /admin/ HTTP/1.1" 302 0
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"POST /api/auth/login/ HTTP/1.1" 200 1078
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /admin/ HTTP/1.1" 302 0
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
/app/mtbrmg_erp/urls.py changed, reloading.
Watching for file changes with StatReloader
/app/mtbrmg_erp/urls.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /api/health/ HTTP/1.1" 200 74
"GET /admin/ HTTP/1.1" 302 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"OPTIONS /api/team/stats/ HTTP/1.1" 200 0
"OPTIONS /api/team/ HTTP/1.1" 200 0
"GET /api/team/ HTTP/1.1" 200 2432
"GET /api/team/stats/ HTTP/1.1" 200 709
"GET /admin/ HTTP/1.1" 302 0
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /admin/ HTTP/1.1" 302 0
Unauthorized: /api/projects/
"GET /api/projects/ HTTP/1.1" 401 62
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /admin/ HTTP/1.1" 302 0
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
Bad Request: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 400 46
Unauthorized: /api/clients/
"GET /api/clients/ HTTP/1.1" 401 143
"GET /admin/ HTTP/1.1" 302 0
Bad Request: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 400 46
Unauthorized: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 401 92
"POST /api/auth/login/ HTTP/1.1" 200 1078
"GET /admin/ HTTP/1.1" 302 0
"GET /api/clients/ HTTP/1.1" 200 52
"POST /api/clients/ HTTP/1.1" 201 251
"GET /admin/ HTTP/1.1" 302 0
"POST /api/projects/ HTTP/1.1" 201 438
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /admin/ HTTP/1.1" 302 0
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"POST /api/auth/login/ HTTP/1.1" 200 1078
"GET /admin/ HTTP/1.1" 302 0
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
"POST /api/auth/logout/ HTTP/1.1" 200 53
"GET /admin/ HTTP/1.1" 302 0
"POST /api/auth/login/ HTTP/1.1" 200 1078
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"POST /api/auth/logout/ HTTP/1.1" 200 53
"POST /api/auth/login/ HTTP/1.1" 200 1078
"GET /admin/ HTTP/1.1" 302 0
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /api/health/ HTTP/1.1" 200 74
"GET /admin/ HTTP/1.1" 302 0
Unauthorized: /api/projects/
"GET /api/projects/ HTTP/1.1" 401 62
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"POST /api/auth/logout/ HTTP/1.1" 200 53
"POST /api/auth/login/ HTTP/1.1" 200 1078
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
Bad Request: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 400 46
"GET /admin/ HTTP/1.1" 302 0
"POST /api/auth/login/ HTTP/1.1" 200 1078
"GET /admin/ HTTP/1.1" 302 0
"POST /api/projects/ HTTP/1.1" 201 303
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"POST /api/auth/login/ HTTP/1.1" 200 1078
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /admin/ HTTP/1.1" 302 0
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
Watching for file changes with StatReloader
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"POST /api/auth/login/ HTTP/1.1" 200 1078
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/team/ HTTP/1.1" 200 2432
"GET /api/team/stats/ HTTP/1.1" 200 709
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
/app/clients/models.py changed, reloading.
Watching for file changes with StatReloader
/app/clients/models.py changed, reloading.
Watching for file changes with StatReloader
/app/projects/models.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/health/ HTTP/1.1" 200 74
/app/tasks/models.py changed, reloading.
Watching for file changes with StatReloader
/app/mtbrmg_erp/urls.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
/app/mtbrmg_erp/settings.py changed, reloading.
Watching for file changes with StatReloader
/app/clients/views.py changed, reloading.
Watching for file changes with StatReloader
/app/clients/views.py changed, reloading.
Watching for file changes with StatReloader
/app/clients/views.py changed, reloading.
Watching for file changes with StatReloader
/app/mtbrmg_erp/settings.py changed, reloading.
Watching for file changes with StatReloader
/app/authentication/models.py changed, reloading.
Watching for file changes with StatReloader
/app/authentication/models.py changed, reloading.
Watching for file changes with StatReloader
/app/authentication/views.py changed, reloading.
Watching for file changes with StatReloader
/app/authentication/views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
Unauthorized: /api/v1/auth/2fa/status/
"GET /api/v1/auth/2fa/status/ HTTP/1.1" 401 62
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
Unauthorized: /api/v1/auth/2fa/status/
"GET /api/v1/auth/2fa/status/ HTTP/1.1" 401 62
Unauthorized: /api/auth/2fa/status/
"GET /api/auth/2fa/status/ HTTP/1.1" 401 62
Unauthorized: /api/v1/auth/login/
"POST /api/v1/auth/login/ HTTP/1.1" 401 92
Unauthorized: /api/v1/auth/login/
"POST /api/v1/auth/login/ HTTP/1.1" 401 92
Unauthorized: /api/v1/auth/login/
"POST /api/v1/auth/login/ HTTP/1.1" 401 92
Unauthorized: /api/v1/auth/login/
"POST /api/v1/auth/login/ HTTP/1.1" 401 92
Unauthorized: /api/v1/auth/login/
"POST /api/v1/auth/login/ HTTP/1.1" 401 92
Unauthorized: /api/v1/auth/login/
"POST /api/v1/auth/login/ HTTP/1.1" 401 92
Unauthorized: /api/v1/auth/login/
"POST /api/v1/auth/login/ HTTP/1.1" 401 92
Unauthorized: /api/v1/auth/login/
"POST /api/v1/auth/login/ HTTP/1.1" 401 92
Unauthorized: /api/v1/auth/login/
"POST /api/v1/auth/login/ HTTP/1.1" 401 92
Unauthorized: /api/v1/auth/login/
"POST /api/v1/auth/login/ HTTP/1.1" 401 92
Unauthorized: /api/v1/auth/login/
"POST /api/v1/auth/login/ HTTP/1.1" 401 92
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
Unauthorized: /api/v1/auth/2fa/status/
"GET /api/v1/auth/2fa/status/ HTTP/1.1" 401 62
Unauthorized: /api/auth/2fa/status/
"GET /api/auth/2fa/status/ HTTP/1.1" 401 62
"POST /api/v1/auth/login/ HTTP/1.1" 200 1022
Internal Server Error: /api/v1/auth/2fa/status/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
psycopg2.errors.UndefinedTable: relation "django_otp_totpdevice" does not exist
LINE 1: SELECT 1 AS "a" FROM "django_otp_totpdevice" WHERE ("django_...
                             ^


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/views/decorators/csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/views/generic/base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/usr/local/lib/python3.11/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/usr/local/lib/python3.11/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/rest_framework/decorators.py", line 50, in handler
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/app/authentication/views.py", line 214, in mfa_status
    'mfa_enabled': MFAService.is_mfa_enabled(user),
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/authentication/services.py", line 137, in is_mfa_enabled
    return TOTPDevice.objects.filter(user=user, confirmed=True).exists()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/query.py", line 1241, in exists
    return self.query.has_results(using=self.db)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/sql/query.py", line 598, in has_results
    return compiler.has_results()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/sql/compiler.py", line 1530, in has_results
    return bool(self.execute_sql(SINGLE))
                ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/sql/compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.11/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
  File "/usr/local/lib/python3.11/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.11/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.ProgrammingError: relation "django_otp_totpdevice" does not exist
LINE 1: SELECT 1 AS "a" FROM "django_otp_totpdevice" WHERE ("django_...
                             ^

"GET /api/v1/auth/2fa/status/ HTTP/1.1" 500 154051
Internal Server Error: /api/v1/auth/2fa/setup/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
psycopg2.errors.UndefinedTable: relation "django_otp_totpdevice" does not exist
LINE 1: SELECT 1 AS "a" FROM "django_otp_totpdevice" WHERE ("django_...
                             ^


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/views/decorators/csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/views/generic/base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/usr/local/lib/python3.11/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/usr/local/lib/python3.11/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/rest_framework/decorators.py", line 50, in handler
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/app/authentication/views.py", line 125, in setup_2fa
    if MFAService.is_mfa_enabled(user):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/authentication/services.py", line 137, in is_mfa_enabled
    return TOTPDevice.objects.filter(user=user, confirmed=True).exists()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/query.py", line 1241, in exists
    return self.query.has_results(using=self.db)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/sql/query.py", line 598, in has_results
    return compiler.has_results()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/sql/compiler.py", line 1530, in has_results
    return bool(self.execute_sql(SINGLE))
                ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/sql/compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.11/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
  File "/usr/local/lib/python3.11/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.11/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.ProgrammingError: relation "django_otp_totpdevice" does not exist
LINE 1: SELECT 1 AS "a" FROM "django_otp_totpdevice" WHERE ("django_...
                             ^

"POST /api/v1/auth/2fa/setup/ HTTP/1.1" 500 154579
Unauthorized: /api/v1/auth/login/
"POST /api/v1/auth/login/ HTTP/1.1" 401 92
Unauthorized: /api/v1/auth/login/
"POST /api/v1/auth/login/ HTTP/1.1" 401 92
Unauthorized: /api/v1/auth/login/
"POST /api/v1/auth/login/ HTTP/1.1" 401 92
Unauthorized: /api/v1/auth/login/
"POST /api/v1/auth/login/ HTTP/1.1" 401 92
Unauthorized: /api/v1/auth/login/
"POST /api/v1/auth/login/ HTTP/1.1" 401 92
Unauthorized: /api/v1/auth/login/
"POST /api/v1/auth/login/ HTTP/1.1" 401 92
Unauthorized: /api/v1/auth/login/
"POST /api/v1/auth/login/ HTTP/1.1" 401 92
Unauthorized: /api/v1/auth/login/
"POST /api/v1/auth/login/ HTTP/1.1" 401 92
Unauthorized: /api/v1/auth/login/
"POST /api/v1/auth/login/ HTTP/1.1" 401 92
Unauthorized: /api/v1/auth/login/
"POST /api/v1/auth/login/ HTTP/1.1" 401 92
"GET /api/v1/clients/ HTTP/1.1" 200 428
"GET /api/v1/clients/ HTTP/1.1" 200 428
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
/app/mtbrmg_erp/settings.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/health/ HTTP/1.1" 200 74
Watching for file changes with StatReloader
"GET /api/health/ HTTP/1.1" 200 74
Unauthorized: /api/v1/auth/2fa/status/
"GET /api/v1/auth/2fa/status/ HTTP/1.1" 401 62
Unauthorized: /api/auth/2fa/status/
"GET /api/auth/2fa/status/ HTTP/1.1" 401 62
"POST /api/v1/auth/login/ HTTP/1.1" 200 1022
"GET /api/v1/auth/2fa/status/ HTTP/1.1" 200 44
"POST /api/v1/auth/2fa/setup/ HTTP/1.1" 200 1627
Unauthorized: /api/v1/auth/login/
"POST /api/v1/auth/login/ HTTP/1.1" 401 92
Unauthorized: /api/v1/auth/login/
"POST /api/v1/auth/login/ HTTP/1.1" 401 92
Unauthorized: /api/v1/auth/login/
"POST /api/v1/auth/login/ HTTP/1.1" 401 92
Unauthorized: /api/v1/auth/login/
"POST /api/v1/auth/login/ HTTP/1.1" 401 92
Unauthorized: /api/v1/auth/login/
"POST /api/v1/auth/login/ HTTP/1.1" 401 92
Unauthorized: /api/v1/auth/login/
"POST /api/v1/auth/login/ HTTP/1.1" 401 92
Unauthorized: /api/v1/auth/login/
"POST /api/v1/auth/login/ HTTP/1.1" 401 92
Unauthorized: /api/v1/auth/login/
"POST /api/v1/auth/login/ HTTP/1.1" 401 92
Unauthorized: /api/v1/auth/login/
"POST /api/v1/auth/login/ HTTP/1.1" 401 92
Unauthorized: /api/v1/auth/login/
"POST /api/v1/auth/login/ HTTP/1.1" 401 92
"GET /api/v1/clients/ HTTP/1.1" 200 428
"GET /api/v1/clients/ HTTP/1.1" 200 428
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
Watching for file changes with StatReloader
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"POST /api/auth/login/ HTTP/1.1" 200 1078
"GET /api/health/ HTTP/1.1" 200 74
"POST /api/v1/auth/login/ HTTP/1.1" 200 1022
"GET /api/v1/auth/2fa/status/ HTTP/1.1" 200 44
"POST /api/v1/auth/2fa/setup/ HTTP/1.1" 200 1651
"GET /api/v1/clients/ HTTP/1.1" 200 428
"GET /api/v1/projects/ HTTP/1.1" 200 984
"GET /api/v1/auth/2fa/status/ HTTP/1.1" 200 44
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"POST /api/auth/logout/ HTTP/1.1" 200 53
"POST /api/auth/login/ HTTP/1.1" 200 1078
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/health/ HTTP/1.1" 200 74
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 352
"GET /api/auth/profile/ HTTP/1.1" 200 352
/app/projects/serializers.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/health/ HTTP/1.1" 200 74
/app/projects/serializers.py changed, reloading.
Watching for file changes with StatReloader
/app/projects/views.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/health/ HTTP/1.1" 200 74
/app/projects/views.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"OPTIONS /auth/profile/ HTTP/1.1" 200 0
"OPTIONS /auth/profile/ HTTP/1.1" 200 0
"OPTIONS /auth/profile/ HTTP/1.1" 200 0
Not Found: /auth/profile/
"GET /auth/profile/ HTTP/1.1" 404 3443
Not Found: /auth/profile/
"GET /auth/profile/ HTTP/1.1" 404 3443
Not Found: /auth/profile/
"GET /auth/profile/ HTTP/1.1" 404 3443
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
Bad Request: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 400 46
"GET /api/health/ HTTP/1.1" 200 74
"POST /api/auth/login/ HTTP/1.1" 200 1168
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
Watching for file changes with StatReloader
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"POST /api/auth/login/ HTTP/1.1" 200 1168
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
Watching for file changes with StatReloader
"GET /api/health/ HTTP/1.1" 200 74
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"POST /api/auth/login/ HTTP/1.1" 200 1168
"OPTIONS /api/clients/ HTTP/1.1" 200 0
"OPTIONS /api/projects/ HTTP/1.1" 200 0
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/projects/ HTTP/1.1" 200 898
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/auth/profile/ HTTP/1.1" 200 372
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/clients/ HTTP/1.1" 200 0
"OPTIONS /api/projects/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
Unauthorized: /api/projects/
Unauthorized: /api/clients/
Unauthorized: /api/auth/profile/
"GET /api/auth/profile/ HTTP/1.1" 401 85
"GET /api/projects/ HTTP/1.1" 401 85
"GET /api/clients/ HTTP/1.1" 401 85
"OPTIONS /api/auth/refresh/ HTTP/1.1" 200 0
"OPTIONS /api/auth/refresh/ HTTP/1.1" 200 0
"OPTIONS /api/auth/refresh/ HTTP/1.1" 200 0
Unauthorized: /api/auth/profile/
"GET /api/auth/profile/ HTTP/1.1" 401 85
"POST /api/auth/refresh/ HTTP/1.1" 200 750
"POST /api/auth/refresh/ HTTP/1.1" 200 750
"POST /api/auth/refresh/ HTTP/1.1" 200 750
"POST /api/auth/refresh/ HTTP/1.1" 200 750
Unauthorized: /api/auth/profile/
"GET /api/auth/profile/ HTTP/1.1" 401 85
Unauthorized: /api/clients/
"GET /api/clients/ HTTP/1.1" 401 85
Unauthorized: /api/projects/
"GET /api/projects/ HTTP/1.1" 401 85
Unauthorized: /api/auth/profile/
"GET /api/auth/profile/ HTTP/1.1" 401 85
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"POST /api/auth/login/ HTTP/1.1" 200 1168
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/auth/profile/ HTTP/1.1" 200 372
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"POST /api/auth/login/ HTTP/1.1" 200 1168
"OPTIONS /api/projects/ HTTP/1.1" 200 0
"OPTIONS /api/clients/ HTTP/1.1" 200 0
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
Watching for file changes with StatReloader
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/clients/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
"POST /api/auth/logout/ HTTP/1.1" 200 53
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"POST /api/auth/login/ HTTP/1.1" 200 1168
"GET /api/health/ HTTP/1.1" 200 74
"OPTIONS /api/projects/ HTTP/1.1" 200 0
"OPTIONS /api/clients/ HTTP/1.1" 200 0
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/clients/ HTTP/1.1" 200 391
"POST /api/auth/logout/ HTTP/1.1" 200 53
Unauthorized: /api/auth/logout/
"POST /api/auth/logout/ HTTP/1.1" 401 62
Unauthorized: /api/auth/logout/
"POST /api/auth/logout/ HTTP/1.1" 401 62
Unauthorized: /api/auth/logout/
"POST /api/auth/logout/ HTTP/1.1" 401 62
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"POST /api/auth/login/ HTTP/1.1" 200 1168
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
Watching for file changes with StatReloader
Unauthorized: /api/auth/profile/
"GET /api/auth/profile/ HTTP/1.1" 401 286
Unauthorized: /api/auth/profile/
"OPTIONS /api/auth/refresh/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 401 286
Unauthorized: /api/auth/profile/
"GET /api/auth/profile/ HTTP/1.1" 401 286
Unauthorized: /api/auth/refresh/
"POST /api/auth/refresh/ HTTP/1.1" 401 116
Unauthorized: /api/auth/refresh/
Unauthorized: /api/auth/refresh/
"POST /api/auth/refresh/ HTTP/1.1" 401 116
"POST /api/auth/refresh/ HTTP/1.1" 401 116
"POST /api/auth/login/ HTTP/1.1" 200 1089
"GET /api/projects/ HTTP/1.1" 200 549
"GET /api/clients/ HTTP/1.1" 200 2287
"GET /api/clients/ HTTP/1.1" 200 2287
"GET /api/clients/ HTTP/1.1" 200 2287
Watching for file changes with StatReloader
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
Unauthorized: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 401 92
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
Unauthorized: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 401 92
"POST /api/auth/login/ HTTP/1.1" 200 1168
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"POST /api/auth/login/ HTTP/1.1" 200 1168
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"POST /api/auth/logout/ HTTP/1.1" 200 53
"POST /api/auth/login/ HTTP/1.1" 200 1168
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
Watching for file changes with StatReloader
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"POST /api/auth/logout/ HTTP/1.1" 200 53
"POST /api/auth/login/ HTTP/1.1" 200 1168
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/health/ HTTP/1.1" 200 74
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/login/?next=/admin/ HTTP/1.1" 200 4350
"GET /static/admin/css/responsive.css HTTP/1.1" 200 18559
"GET /static/admin/css/login.css HTTP/1.1" 200 958
"GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2694
"GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2929
"GET /static/admin/css/base.css HTTP/1.1" 200 21310
"GET /static/admin/css/rtl.css HTTP/1.1" 200 4918
"GET /static/admin/css/responsive_rtl.css HTTP/1.1" 200 1864
"GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
"GET /static/admin/js/theme.js HTTP/1.1" 200 1943
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 3437
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"POST /api/auth/logout/ HTTP/1.1" 200 53
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"POST /api/auth/login/ HTTP/1.1" 200 1168
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/health/ HTTP/1.1" 200 74
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"POST /api/auth/login/ HTTP/1.1" 200 1168
"GET /api/health/ HTTP/1.1" 200 74
"POST /api/auth/logout/ HTTP/1.1" 200 53
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"POST /api/auth/login/ HTTP/1.1" 200 1168
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"POST /api/auth/login/ HTTP/1.1" 200 1168
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
Watching for file changes with StatReloader
"POST /api/auth/login/ HTTP/1.1" 200 1089
"GET /api/projects/ HTTP/1.1" 200 549
"GET /api/clients/ HTTP/1.1" 200 2287
"GET /api/clients/ HTTP/1.1" 200 2287
"GET /api/clients/ HTTP/1.1" 200 2287
"GET /api/projects/ HTTP/1.1" 200 549
"GET /api/clients/ HTTP/1.1" 200 2287
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Not Found: /
"GET / HTTP/1.1" 404 3386
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 3437
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Not Found: /
"GET / HTTP/1.1" 404 3386
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 3437
Not Found: /
"GET / HTTP/1.1" 404 3386
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 3437
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/tasks/ HTTP/1.1" 200 0
"OPTIONS /api/tasks/ HTTP/1.1" 200 0
"OPTIONS /api/projects/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/projects/ HTTP/1.1" 200 549
"GET /api/tasks/ HTTP/1.1" 200 947
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/tasks/ HTTP/1.1" 200 947
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
Watching for file changes with StatReloader
"GET /api/health/ HTTP/1.1" 200 74
Unauthorized: /api/auth/profile/
"GET /api/auth/profile/ HTTP/1.1" 401 286
Unauthorized: /api/auth/profile/
"GET /api/auth/profile/ HTTP/1.1" 401 286
"OPTIONS /api/auth/refresh/ HTTP/1.1" 200 0
Unauthorized: /api/auth/refresh/
"POST /api/auth/refresh/ HTTP/1.1" 401 116
Unauthorized: /api/auth/refresh/
"POST /api/auth/refresh/ HTTP/1.1" 401 116
"GET /api/health/ HTTP/1.1" 200 74
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"POST /api/auth/login/ HTTP/1.1" 200 1168
"OPTIONS /api/clients/ HTTP/1.1" 200 0
"OPTIONS /api/projects/ HTTP/1.1" 200 0
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
Watching for file changes with StatReloader
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
"POST /api/auth/logout/ HTTP/1.1" 200 53
"POST /api/auth/login/ HTTP/1.1" 200 1168
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/health/ HTTP/1.1" 200 74
"POST /api/auth/login/ HTTP/1.1" 200 1168
Bad Request: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 400 46
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/health/ HTTP/1.1" 200 74
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
Unauthorized: /api/tasks/
"GET /api/tasks/ HTTP/1.1" 401 62
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
Unauthorized: /api/team/
"GET /api/team/ HTTP/1.1" 401 62
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"POST /api/auth/logout/ HTTP/1.1" 200 53
"POST /api/auth/login/ HTTP/1.1" 200 1168
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"POST /api/auth/login/ HTTP/1.1" 200 1168
"OPTIONS /api/projects/ HTTP/1.1" 200 0
"OPTIONS /api/clients/ HTTP/1.1" 200 0
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/clients/ HTTP/1.1" 200 391
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/projects/ HTTP/1.1" 200 898
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"OPTIONS /api/team/ HTTP/1.1" 200 0
"OPTIONS /api/team/stats/ HTTP/1.1" 200 0
"GET /api/team/ HTTP/1.1" 200 2432
"GET /api/team/stats/ HTTP/1.1" 200 709
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/team/ HTTP/1.1" 200 2432
"GET /api/team/stats/ HTTP/1.1" 200 709
"OPTIONS /api/users/ HTTP/1.1" 200 0
Not Found: /api/users/
"GET /api/users/ HTTP/1.1" 404 17176
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/auth/profile/ HTTP/1.1" 200 372
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"OPTIONS /api/api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/api/auth/profile/ HTTP/1.1" 200 0
Not Found: /api/api/auth/profile/
"GET /api/api/auth/profile/ HTTP/1.1" 404 17209
Not Found: /api/api/auth/profile/
"GET /api/api/auth/profile/ HTTP/1.1" 404 17209
Not Found: /api/api/tasks/
"GET /api/api/tasks/ HTTP/1.1" 404 17188
Not Found: /api/api/projects/
"GET /api/api/projects/ HTTP/1.1" 404 17197
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"OPTIONS /api/api/auth/login/ HTTP/1.1" 200 0
Not Found: /api/api/auth/login/
"POST /api/api/auth/login/ HTTP/1.1" 404 17204
Not Found: /api/api/auth/login/
"POST /api/api/auth/login/ HTTP/1.1" 404 17204
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"POST /api/auth/login/ HTTP/1.1" 200 1168
Not Found: /api/api/auth/login/
"POST /api/api/auth/login/ HTTP/1.1" 404 17204
"GET /api/health/ HTTP/1.1" 200 74
Not Found: /api/api/auth/login/
"POST /api/api/auth/login/ HTTP/1.1" 404 17204
"OPTIONS /api/api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/api/auth/profile/ HTTP/1.1" 200 0
Not Found: /api/api/auth/profile/
"GET /api/api/auth/profile/ HTTP/1.1" 404 17209
Not Found: /api/api/auth/profile/
"GET /api/api/auth/profile/ HTTP/1.1" 404 17209
"OPTIONS /api/api/auth/login/ HTTP/1.1" 200 0
Not Found: /api/api/auth/login/
"POST /api/api/auth/login/ HTTP/1.1" 404 17204
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
Not Found: /api/api/auth/login/
"POST /api/api/auth/login/ HTTP/1.1" 404 17204
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
Unauthorized: /api/
"GET /api/ HTTP/1.1" 401 62
"GET /api/health/ HTTP/1.1" 200 74
"GET /admin/ HTTP/1.1" 302 0
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
Not Found: /api/api/auth/login/
"POST /api/api/auth/login/ HTTP/1.1" 404 17204
"GET /api/health/ HTTP/1.1" 200 74
Not Found: /api/api/auth/login/
"POST /api/api/auth/login/ HTTP/1.1" 404 17204
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
Unauthorized: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 401 92
"POST /api/auth/login/ HTTP/1.1" 200 1168
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
Not Found: /api/api/auth/login/
"POST /api/api/auth/login/ HTTP/1.1" 404 17204
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/health/ HTTP/1.1" 200 74
Watching for file changes with StatReloader
"GET /api/health/ HTTP/1.1" 200 74
"POST /api/auth/login/ HTTP/1.1" 200 965
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"POST /api/auth/login/ HTTP/1.1" 200 965
"POST /api/auth/login/ HTTP/1.1" 200 965
"POST /api/auth/login/ HTTP/1.1" 200 950
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"POST /api/auth/login/ HTTP/1.1" 200 950
"OPTIONS /api/projects/ HTTP/1.1" 200 0
"OPTIONS /api/clients/ HTTP/1.1" 200 0
"GET /api/projects/ HTTP/1.1" 200 52
"GET /api/clients/ HTTP/1.1" 200 52
"GET /api/clients/ HTTP/1.1" 200 52
"GET /api/clients/ HTTP/1.1" 200 52
"GET /api/clients/ HTTP/1.1" 200 52
"GET /api/projects/ HTTP/1.1" 200 52
"GET /api/clients/ HTTP/1.1" 200 52
"GET /api/clients/ HTTP/1.1" 200 52
"GET /api/clients/ HTTP/1.1" 200 52
"GET /api/projects/ HTTP/1.1" 200 52
"GET /api/health/ HTTP/1.1" 200 74
Watching for file changes with StatReloader
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
Watching for file changes with StatReloader
"GET /api/health/ HTTP/1.1" 200 74
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"POST /api/auth/login/ HTTP/1.1" 200 950
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"POST /api/auth/login/ HTTP/1.1" 200 950
"OPTIONS /api/projects/ HTTP/1.1" 200 0
"OPTIONS /api/clients/ HTTP/1.1" 200 0
"GET /api/clients/ HTTP/1.1" 200 52
"GET /api/projects/ HTTP/1.1" 200 52
"GET /api/clients/ HTTP/1.1" 200 52
"GET /api/clients/ HTTP/1.1" 200 52
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"OPTIONS /api/tasks/ HTTP/1.1" 200 0
"GET /api/tasks/ HTTP/1.1" 200 52
"GET /api/projects/ HTTP/1.1" 200 52
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/tasks/ HTTP/1.1" 200 52
"GET /api/projects/ HTTP/1.1" 200 52
"OPTIONS /api/team/ HTTP/1.1" 200 0
"GET /api/team/ HTTP/1.1" 200 52
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/projects/ HTTP/1.1" 200 52
"GET /api/tasks/ HTTP/1.1" 200 52
"GET /api/team/ HTTP/1.1" 200 52
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/tasks/ HTTP/1.1" 200 52
"GET /api/projects/ HTTP/1.1" 200 52
"GET /api/tasks/ HTTP/1.1" 200 52
"GET /api/projects/ HTTP/1.1" 200 52
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/projects/ HTTP/1.1" 200 52
"GET /api/tasks/ HTTP/1.1" 200 52
"GET /api/team/ HTTP/1.1" 200 52
"GET /api/team/ HTTP/1.1" 200 52
"OPTIONS /api/team/stats/ HTTP/1.1" 200 0
"GET /api/team/ HTTP/1.1" 200 52
"GET /api/team/stats/ HTTP/1.1" 200 218
"OPTIONS /api/users/ HTTP/1.1" 200 0
Not Found: /api/users/
"GET /api/users/ HTTP/1.1" 404 17176
Watching for file changes with StatReloader
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/tasks/ HTTP/1.1" 200 52
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/tasks/ HTTP/1.1" 200 52
"GET /api/projects/ HTTP/1.1" 200 52
"GET /api/team/ HTTP/1.1" 200 52
"GET /api/team/ HTTP/1.1" 200 52
"GET /api/team/stats/ HTTP/1.1" 200 218
Not Found: /api/users/
"GET /api/users/ HTTP/1.1" 404 17176
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/team/ HTTP/1.1" 200 52
"GET /api/team/stats/ HTTP/1.1" 200 218
Not Found: /api/users/
"GET /api/users/ HTTP/1.1" 404 17176
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/authentication/views.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/authentication/views.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/authentication/urls.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/authentication/urls.py changed, reloading.
Watching for file changes with StatReloader
"POST /api/auth/login/ HTTP/1.1" 200 950
"GET /api/auth/users/ HTTP/1.1" 200 584
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/team/ HTTP/1.1" 200 52
"GET /api/team/stats/ HTTP/1.1" 200 218
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/team/ HTTP/1.1" 200 52
"GET /api/team/stats/ HTTP/1.1" 200 218
"OPTIONS /api/auth/users/ HTTP/1.1" 200 0
"GET /api/auth/users/ HTTP/1.1" 200 584
"GET /api/projects/ HTTP/1.1" 200 52
"GET /api/clients/ HTTP/1.1" 200 52
"GET /api/projects/ HTTP/1.1" 200 52
"GET /api/tasks/ HTTP/1.1" 200 52
"GET /api/team/ HTTP/1.1" 200 52
"GET /api/team/stats/ HTTP/1.1" 200 218
"GET /api/auth/users/ HTTP/1.1" 200 584
Watching for file changes with StatReloader
"GET /api/health/ HTTP/1.1" 200 74
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"POST /api/auth/login/ HTTP/1.1" 200 950
"OPTIONS /api/team/departments/sales/ HTTP/1.1" 200 0
"GET /api/team/departments/sales/ HTTP/1.1" 200 52
"OPTIONS /api/team/departments/sales/stats/ HTTP/1.1" 200 0
"GET /api/team/departments/sales/stats/ HTTP/1.1" 200 293
"GET /api/team/ HTTP/1.1" 200 52
"GET /api/team/stats/ HTTP/1.1" 200 218
"GET /api/auth/users/ HTTP/1.1" 200 584
"GET /api/team/departments/sales/ HTTP/1.1" 200 52
"GET /api/team/departments/sales/stats/ HTTP/1.1" 200 293
"OPTIONS /api/team/departments/development/ HTTP/1.1" 200 0
"GET /api/team/departments/development/ HTTP/1.1" 200 52
"OPTIONS /api/team/departments/development/stats/ HTTP/1.1" 200 0
"GET /api/team/departments/development/stats/ HTTP/1.1" 200 291
"OPTIONS /api/team/?department=design HTTP/1.1" 200 0
"GET /api/team/?department=design HTTP/1.1" 200 52
"GET /api/team/stats/ HTTP/1.1" 200 218
"OPTIONS /api/team/?department=media_buying HTTP/1.1" 200 0
"GET /api/team/?department=media_buying HTTP/1.1" 200 52
"GET /api/team/stats/ HTTP/1.1" 200 218
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/mtbrmg_erp/settings.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/mtbrmg_erp/urls.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/finance/admin.py changed, reloading.
Watching for file changes with StatReloader
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"OPTIONS /api/finance/dashboard/overview/ HTTP/1.1" 200 0
Not Found: /api/finance/dashboard/overview/
"GET /api/finance/dashboard/overview/ HTTP/1.1" 404 32681
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
Not Found: /api/finance/dashboard/overview/
"GET /api/finance/dashboard/overview/ HTTP/1.1" 404 32681
"OPTIONS /api/finance/revenue/ HTTP/1.1" 200 0
Not Found: /api/finance/revenue/
"GET /api/finance/revenue/ HTTP/1.1" 404 32648
"OPTIONS /api/finance/expenses/ HTTP/1.1" 200 0
Not Found: /api/finance/expenses/
"GET /api/finance/expenses/ HTTP/1.1" 404 32651
Not Found: /api/finance/expenses/
"GET /api/finance/expenses/ HTTP/1.1" 404 32651
Not Found: /api/finance/revenue/
"GET /api/finance/revenue/ HTTP/1.1" 404 32648
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
Not Found: /api/finance/expenses/
"GET /api/finance/expenses/ HTTP/1.1" 404 32651
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"OPTIONS /api/finance/cash-flow/?period=current_year&type=monthly HTTP/1.1" 200 0
Not Found: /api/finance/cash-flow/
"GET /api/finance/cash-flow/?period=current_year&type=monthly HTTP/1.1" 404 32691
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"OPTIONS /api/finance/budgets/?year=2025 HTTP/1.1" 200 0
Not Found: /api/finance/budgets/
"GET /api/finance/budgets/?year=2025 HTTP/1.1" 404 32658
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"OPTIONS /api/finance/reports/?period=current_month HTTP/1.1" 200 0
Not Found: /api/finance/reports/
"GET /api/finance/reports/?period=current_month HTTP/1.1" 404 32669
Watching for file changes with StatReloader
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /admin HTTP/1.1" 301 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/login/?next=/admin/ HTTP/1.1" 200 4350
"GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2694
"GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2929
"GET /static/admin/css/base.css HTTP/1.1" 200 21310
"GET /static/admin/css/login.css HTTP/1.1" 200 958
"GET /static/admin/css/responsive.css HTTP/1.1" 200 18559
"GET /static/admin/css/rtl.css HTTP/1.1" 200 4918
"GET /static/admin/css/responsive_rtl.css HTTP/1.1" 200 1864
"GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
"GET /static/admin/js/theme.js HTTP/1.1" 200 1943
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 3657
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
Watching for file changes with StatReloader
Unauthorized: /api/auth/profile/
"GET /api/auth/profile/ HTTP/1.1" 401 62
Unauthorized: /api/
"GET /api/ HTTP/1.1" 401 62
Unauthorized: /api/
"GET /api/ HTTP/1.1" 401 62
Unauthorized: /api/
"GET /api/ HTTP/1.1" 401 62
"POST /api/auth/login/ HTTP/1.1" 200 950
Unauthorized: /api/projects/
Unauthorized: /api/tasks/
"GET /api/projects/ HTTP/1.1" 401 62
"GET /api/tasks/ HTTP/1.1" 401 62
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"POST /api/auth/login/ HTTP/1.1" 200 950
"POST /api/auth/login/ HTTP/1.1" 200 950
"POST /api/auth/login/ HTTP/1.1" 200 950
"POST /api/auth/login/ HTTP/1.1" 200 950
"OPTIONS /api/finance/expenses/ HTTP/1.1" 200 0
Not Found: /api/finance/expenses/
"GET /api/finance/expenses/ HTTP/1.1" 404 32651
Watching for file changes with StatReloader
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"POST /api/auth/login/ HTTP/1.1" 200 950
"OPTIONS /api/projects/ HTTP/1.1" 200 0
"OPTIONS /api/clients/ HTTP/1.1" 200 0
"GET /api/clients/ HTTP/1.1" 200 52
"GET /api/projects/ HTTP/1.1" 200 52
"GET /api/projects/ HTTP/1.1" 200 52
"GET /api/clients/ HTTP/1.1" 200 52
"GET /api/clients/ HTTP/1.1" 200 52
"OPTIONS /api/tasks/ HTTP/1.1" 200 0
"GET /api/projects/ HTTP/1.1" 200 52
"GET /api/tasks/ HTTP/1.1" 200 52
"OPTIONS /api/team/departments/sales/ HTTP/1.1" 200 0
"GET /api/team/departments/sales/ HTTP/1.1" 200 52
"OPTIONS /api/team/departments/sales/stats/ HTTP/1.1" 200 0
"GET /api/team/departments/sales/stats/ HTTP/1.1" 200 293
"OPTIONS /api/finance/revenue/ HTTP/1.1" 200 0
Not Found: /api/finance/revenue/
"GET /api/finance/revenue/ HTTP/1.1" 404 32648
Not Found: /api/finance/revenue/
"GET /api/finance/revenue/ HTTP/1.1" 404 32648
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"POST /api/auth/login/ HTTP/1.1" 200 950
- Broken pipe from ('127.0.0.1', 54963)
"POST /api/auth/login/ HTTP/1.1" 200 950
"OPTIONS /api/clients/ HTTP/1.1" 200 0
"OPTIONS /api/projects/ HTTP/1.1" 200 0
"GET /api/clients/ HTTP/1.1" 200 1116
"GET /api/projects/ HTTP/1.1" 200 1479
"OPTIONS /api/tasks/ HTTP/1.1" 200 0
"GET /api/projects/ HTTP/1.1" 200 1479
"GET /api/tasks/ HTTP/1.1" 200 52
"OPTIONS /api/finance/revenue/ HTTP/1.1" 200 0
Not Found: /api/finance/revenue/
"GET /api/finance/revenue/ HTTP/1.1" 404 35257
Not Found: /api/finance/revenue/
"GET /api/finance/revenue/ HTTP/1.1" 404 35257
Not Found: /api/finance/
"GET /api/finance/ HTTP/1.1" 404 35226
Unauthorized: /api/revenue/
"GET /api/revenue/ HTTP/1.1" 401 62
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"POST /api/auth/login/ HTTP/1.1" 200 950
"OPTIONS /api/revenue/ HTTP/1.1" 200 0
"GET /api/revenue/ HTTP/1.1" 200 1284
"OPTIONS /api/revenue/summary/ HTTP/1.1" 200 0
"GET /api/revenue/summary/ HTTP/1.1" 200 413
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /api/health/ HTTP/1.1" 200 74
Unauthorized: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 401 92
Unauthorized: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 401 92
"POST /api/auth/login/ HTTP/1.1" 200 950
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"POST /api/auth/login/ HTTP/1.1" 200 950
Not Found: /index.html
"GET /index.html HTTP/1.1" 404 3645
Not Found: /index.html
"GET /index.html HTTP/1.1" 404 3645
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"POST /api/auth/login/ HTTP/1.1" 200 950
"OPTIONS /api/projects/ HTTP/1.1" 200 0
"OPTIONS /api/clients/ HTTP/1.1" 200 0
"GET /api/clients/ HTTP/1.1" 200 1116
"GET /api/projects/ HTTP/1.1" 200 1479
"OPTIONS /api/tasks/ HTTP/1.1" 200 0
"GET /api/projects/ HTTP/1.1" 200 1479
"GET /api/tasks/ HTTP/1.1" 200 1277
"OPTIONS /api/team/ HTTP/1.1" 200 0
"GET /api/team/ HTTP/1.1" 200 666
"OPTIONS /api/team/departments/sales/ HTTP/1.1" 200 0
"GET /api/team/departments/sales/ HTTP/1.1" 200 52
"OPTIONS /api/team/departments/sales/stats/ HTTP/1.1" 200 0
"GET /api/team/departments/sales/stats/ HTTP/1.1" 200 293
"OPTIONS /api/team/departments/development/ HTTP/1.1" 200 0
"GET /api/team/departments/development/ HTTP/1.1" 200 52
"OPTIONS /api/team/departments/development/stats/ HTTP/1.1" 200 0
"GET /api/team/departments/development/stats/ HTTP/1.1" 200 291
"OPTIONS /api/team/?department=design HTTP/1.1" 200 0
"GET /api/team/?department=design HTTP/1.1" 200 52
"OPTIONS /api/team/stats/ HTTP/1.1" 200 0
"GET /api/team/stats/ HTTP/1.1" 200 327
"OPTIONS /api/team/?department=media_buying HTTP/1.1" 200 0
"GET /api/team/?department=media_buying HTTP/1.1" 200 52
"GET /api/team/stats/ HTTP/1.1" 200 327
"OPTIONS /api/revenue/ HTTP/1.1" 200 0
"GET /api/revenue/ HTTP/1.1" 200 1284
"OPTIONS /api/revenue/summary/ HTTP/1.1" 200 0
"GET /api/revenue/summary/ HTTP/1.1" 200 413
"OPTIONS /api/expenses/ HTTP/1.1" 200 0
"GET /api/expenses/ HTTP/1.1" 200 1025
"OPTIONS /api/expenses/summary/ HTTP/1.1" 200 0
"GET /api/expenses/summary/ HTTP/1.1" 200 545
"OPTIONS /api/expense-categories/ HTTP/1.1" 200 0
"GET /api/expense-categories/ HTTP/1.1" 200 970
"OPTIONS /api/cash-flow/?period=current_year&type=monthly HTTP/1.1" 200 0
"GET /api/cash-flow/?period=current_year&type=monthly HTTP/1.1" 200 52
"OPTIONS /api/cash-flow/summary/ HTTP/1.1" 200 0
Not Found: /api/cash-flow/summary/
"GET /api/cash-flow/summary/ HTTP/1.1" 404 31
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/finance/views.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/cash-flow/summary/ HTTP/1.1" 200 639
"GET /api/cash-flow/ HTTP/1.1" 200 52
"GET /api/cash-flow/summary/ HTTP/1.1" 200 639
"GET /api/cash-flow/summary/ HTTP/1.1" 200 639
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/cash-flow/?period=current_year&type=monthly HTTP/1.1" 200 52
"GET /api/cash-flow/summary/ HTTP/1.1" 200 639
"GET /api/cash-flow/summary/ HTTP/1.1" 200 639
"GET /api/cash-flow/ HTTP/1.1" 200 52
"GET /api/cash-flow/summary/ HTTP/1.1" 200 766
"GET /api/cash-flow/summary/ HTTP/1.1" 200 766
"GET /api/cash-flow/ HTTP/1.1" 200 1589
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/cash-flow/?period=current_year&type=monthly HTTP/1.1" 200 1589
"GET /api/cash-flow/summary/ HTTP/1.1" 200 766
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/cash-flow/?period=current_year&type=monthly HTTP/1.1" 200 1589
"GET /api/cash-flow/summary/ HTTP/1.1" 200 766
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/cash-flow/?period=current_year&type=monthly HTTP/1.1" 200 1589
"GET /api/cash-flow/summary/ HTTP/1.1" 200 766
"GET /api/expenses/ HTTP/1.1" 200 1025
"GET /api/expenses/summary/ HTTP/1.1" 200 545
"GET /api/expense-categories/ HTTP/1.1" 200 970
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/expenses/ HTTP/1.1" 200 1025
"GET /api/expenses/summary/ HTTP/1.1" 200 545
"GET /api/expense-categories/ HTTP/1.1" 200 970
"GET /api/revenue/ HTTP/1.1" 200 1284
"GET /api/revenue/summary/ HTTP/1.1" 200 413
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/revenue/ HTTP/1.1" 200 1284
"GET /api/revenue/summary/ HTTP/1.1" 200 413
"OPTIONS /api/reports/?period=current_month HTTP/1.1" 200 0
Bad Request: /api/reports/
"GET /api/reports/?period=current_month HTTP/1.1" 400 111
"GET /api/reports/ HTTP/1.1" 200 52
Bad Request: /api/reports/
"GET /api/reports/?period=current_month HTTP/1.1" 400 111
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/finance/views.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/reports/?period=current_month HTTP/1.1" 200 52
"GET /api/reports/summary/ HTTP/1.1" 200 104
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"OPTIONS /api/reports/?period=current_month HTTP/1.1" 200 0
"GET /api/reports/?period=current_month HTTP/1.1" 200 52
"OPTIONS /api/reports/summary/ HTTP/1.1" 200 0
"GET /api/reports/summary/ HTTP/1.1" 200 104
"GET /api/reports/?period=current_month HTTP/1.1" 200 52
"GET /api/reports/summary/ HTTP/1.1" 200 104
"GET /api/reports/summary/ HTTP/1.1" 200 104
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/reports/?period=current_month HTTP/1.1" 200 52
"OPTIONS /api/reports/summary/ HTTP/1.1" 200 0
"GET /api/reports/summary/ HTTP/1.1" 200 104
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/reports/?period=current_month HTTP/1.1" 200 52
"GET /api/reports/summary/ HTTP/1.1" 200 104
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/reports/?period=current_month HTTP/1.1" 200 52
"GET /api/reports/summary/ HTTP/1.1" 200 104
"OPTIONS /api/budgets/?year=2025 HTTP/1.1" 200 0
"GET /api/budgets/?year=2025 HTTP/1.1" 200 52
"OPTIONS /api/budgets/summary/ HTTP/1.1" 200 0
Not Found: /api/budgets/summary/
"GET /api/budgets/summary/ HTTP/1.1" 404 31
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/finance/views.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/finance/views.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/finance/views.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/budgets/summary/ HTTP/1.1" 200 424
"GET /api/budgets/ HTTP/1.1" 200 52
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/budgets/?year=2025 HTTP/1.1" 200 52
"GET /api/budgets/summary/ HTTP/1.1" 200 424
Internal Server Error: /api/budgets/summary/
Traceback (most recent call last):
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/views/decorators/csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/finance/views.py", line 532, in summary
    'total_budget': float(budget.total_budget),
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: float() argument must be a string or a real number, not 'Money'
"GET /api/budgets/summary/ HTTP/1.1" 500 104319
Internal Server Error: /api/budgets/summary/
Traceback (most recent call last):
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/views/decorators/csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/finance/views.py", line 532, in summary
    'total_budget': float(budget.total_budget),
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: float() argument must be a string or a real number, not 'Money'
"GET /api/budgets/summary/ HTTP/1.1" 500 104319
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/finance/views.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/finance/views.py changed, reloading.
Watching for file changes with StatReloader
Internal Server Error: /api/budgets/summary/
Traceback (most recent call last):
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/views/decorators/csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/finance/views.py", line 550, in summary
    'total_budget_amount': float(total_budget_amount.amount) if total_budget_amount else 0.0,
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'decimal.Decimal' object has no attribute 'amount'
"GET /api/budgets/summary/ HTTP/1.1" 500 106288
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/finance/views.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/finance/views.py changed, reloading.
Watching for file changes with StatReloader
Internal Server Error: /api/budgets/summary/
Traceback (most recent call last):
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/views/decorators/csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/finance/views.py", line 532, in summary
    'total_budget': float(budget.total_budget) if budget.total_budget else 0.0,
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: float() argument must be a string or a real number, not 'Money'
"GET /api/budgets/summary/ HTTP/1.1" 500 104415
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/finance/views.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/finance/views.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/finance/views.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/budgets/summary/ HTTP/1.1" 200 1625
"GET /api/budgets/summary/ HTTP/1.1" 200 1625
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/budgets/?year=2025 HTTP/1.1" 200 1292
"GET /api/budgets/summary/ HTTP/1.1" 200 1625
"GET /api/budgets/ HTTP/1.1" 200 1292
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/budgets/?year=2025 HTTP/1.1" 200 1292
"GET /api/budgets/summary/ HTTP/1.1" 200 1625
Watching for file changes with StatReloader
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/reports/?period=current_month HTTP/1.1" 200 52
"GET /api/reports/summary/ HTTP/1.1" 200 104
"GET /api/budgets/?year=2025 HTTP/1.1" 200 1292
"GET /api/budgets/summary/ HTTP/1.1" 200 1625
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/budgets/?year=2025 HTTP/1.1" 200 1292
"GET /api/budgets/summary/ HTTP/1.1" 200 1625
Unauthorized: /api/
"GET /api/ HTTP/1.1" 401 62
Unauthorized: /api/
"GET /api/ HTTP/1.1" 401 62
Unauthorized: /api/
"GET /api/ HTTP/1.1" 401 62
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"OPTIONS /api/projects/ HTTP/1.1" 200 0
"OPTIONS /api/clients/ HTTP/1.1" 200 0
"GET /api/clients/ HTTP/1.1" 200 1116
"GET /api/projects/ HTTP/1.1" 200 1479
"GET /api/clients/ HTTP/1.1" 200 1116
"GET /api/clients/ HTTP/1.1" 200 1116
"OPTIONS /api/tasks/ HTTP/1.1" 200 0
"GET /api/projects/ HTTP/1.1" 200 1479
"GET /api/tasks/ HTTP/1.1" 200 1277
"OPTIONS /api/team/ HTTP/1.1" 200 0
"GET /api/team/ HTTP/1.1" 200 666
"OPTIONS /api/budgets/?year=2025 HTTP/1.1" 200 0
"GET /api/budgets/?year=2025 HTTP/1.1" 200 1292
"OPTIONS /api/budgets/summary/ HTTP/1.1" 200 0
"GET /api/budgets/summary/ HTTP/1.1" 200 1625
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/budgets/?year=2025 HTTP/1.1" 200 1292
"GET /api/budgets/summary/ HTTP/1.1" 200 1625
"OPTIONS /api/team/departments/sales/ HTTP/1.1" 200 0
"GET /api/team/departments/sales/ HTTP/1.1" 200 52
"OPTIONS /api/team/departments/sales/stats/ HTTP/1.1" 200 0
"GET /api/team/departments/sales/stats/ HTTP/1.1" 200 293
"OPTIONS /api/team/stats/ HTTP/1.1" 200 0
"GET /api/team/ HTTP/1.1" 200 666
"GET /api/team/stats/ HTTP/1.1" 200 327
"OPTIONS /api/auth/users/ HTTP/1.1" 200 0
"GET /api/auth/users/ HTTP/1.1" 200 584
"OPTIONS /api/revenue/ HTTP/1.1" 200 0
"GET /api/revenue/ HTTP/1.1" 200 1284
"OPTIONS /api/revenue/summary/ HTTP/1.1" 200 0
"GET /api/revenue/summary/ HTTP/1.1" 200 413
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Unauthorized: /api/
"GET /api/ HTTP/1.1" 401 62
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"POST /api/auth/login/ HTTP/1.1" 200 950
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"OPTIONS /api/clients/?page_size=100 HTTP/1.1" 200 0
"OPTIONS /api/projects/?page_size=100 HTTP/1.1" 200 0
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/projects/?page_size=100 HTTP/1.1" 200 1479
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/projects/?page_size=100 HTTP/1.1" 200 1479
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/projects/?page_size=100 HTTP/1.1" 200 1479
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"OPTIONS /api/auth/users/?role=sales_manager,admin&page_size=100 HTTP/1.1" 200 0
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
Bad Request: /api/auth/users/
"GET /api/auth/users/?role=sales_manager,admin&page_size=100 HTTP/1.1" 400 115
"GET /api/projects/?page_size=100 HTTP/1.1" 200 1479
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
Bad Request: /api/auth/users/
"GET /api/auth/users/?role=sales_manager,admin&page_size=100 HTTP/1.1" 400 115
"GET /api/projects/?page_size=100 HTTP/1.1" 200 1479
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
Bad Request: /api/auth/users/
"GET /api/auth/users/?role=sales_manager,admin&page_size=100 HTTP/1.1" 400 115
"GET /api/projects/?page_size=100 HTTP/1.1" 200 1479
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
Bad Request: /api/auth/users/
"GET /api/auth/users/?role=sales_manager,admin&page_size=100 HTTP/1.1" 400 115
"GET /api/projects/?page_size=100 HTTP/1.1" 200 1479
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
Bad Request: /api/auth/users/
"GET /api/auth/users/?role=sales_manager,admin&page_size=100 HTTP/1.1" 400 115
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/projects/?page_size=100 HTTP/1.1" 200 1479
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"OPTIONS /api/auth/users/?page_size=100 HTTP/1.1" 200 0
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/auth/users/?page_size=100 HTTP/1.1" 200 584
"GET /api/projects/?page_size=100 HTTP/1.1" 200 1479
"OPTIONS /api/revenue/ HTTP/1.1" 200 0
"GET /api/revenue/ HTTP/1.1" 200 1284
"OPTIONS /api/revenue/summary/ HTTP/1.1" 200 0
"GET /api/revenue/summary/ HTTP/1.1" 200 413
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/auth/users/?page_size=100 HTTP/1.1" 200 584
"GET /api/projects/?page_size=100 HTTP/1.1" 200 1479
"OPTIONS /api/expenses/ HTTP/1.1" 200 0
"GET /api/expenses/ HTTP/1.1" 200 1025
"OPTIONS /api/expenses/summary/ HTTP/1.1" 200 0
"GET /api/expenses/summary/ HTTP/1.1" 200 545
"OPTIONS /api/expense-categories/ HTTP/1.1" 200 0
"GET /api/expense-categories/ HTTP/1.1" 200 970
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/expenses/ HTTP/1.1" 200 1025
"GET /api/expenses/summary/ HTTP/1.1" 200 545
"GET /api/expense-categories/ HTTP/1.1" 200 970
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/expense-categories/ HTTP/1.1" 200 970
"GET /api/auth/users/?page_size=100 HTTP/1.1" 200 584
"GET /api/projects/?page_size=100 HTTP/1.1" 200 1479
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/expense-categories/ HTTP/1.1" 200 970
"GET /api/auth/users/?page_size=100 HTTP/1.1" 200 584
"GET /api/projects/?page_size=100 HTTP/1.1" 200 1479
"OPTIONS /api/cash-flow/?period=current_year&type=monthly HTTP/1.1" 200 0
"GET /api/cash-flow/?period=current_year&type=monthly HTTP/1.1" 200 1589
"OPTIONS /api/cash-flow/summary/ HTTP/1.1" 200 0
"GET /api/cash-flow/summary/ HTTP/1.1" 200 766
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"OPTIONS /api/clients/ HTTP/1.1" 200 0
"OPTIONS /api/projects/ HTTP/1.1" 200 0
"GET /api/clients/ HTTP/1.1" 200 1116
"GET /api/projects/ HTTP/1.1" 200 1479
"GET /api/clients/ HTTP/1.1" 200 1116
"GET /api/clients/ HTTP/1.1" 200 1116
"OPTIONS /api/tasks/ HTTP/1.1" 200 0
"GET /api/projects/ HTTP/1.1" 200 1479
"GET /api/tasks/ HTTP/1.1" 200 1277
"OPTIONS /api/team/ HTTP/1.1" 200 0
"GET /api/team/ HTTP/1.1" 200 666
"OPTIONS /api/team/departments/sales/ HTTP/1.1" 200 0
"GET /api/team/departments/sales/ HTTP/1.1" 200 52
"OPTIONS /api/team/departments/sales/stats/ HTTP/1.1" 200 0
"GET /api/team/departments/sales/stats/ HTTP/1.1" 200 293
"OPTIONS /api/team/departments/development/ HTTP/1.1" 200 0
"GET /api/team/departments/development/ HTTP/1.1" 200 52
"OPTIONS /api/team/departments/development/stats/ HTTP/1.1" 200 0
"GET /api/team/departments/development/stats/ HTTP/1.1" 200 291
"OPTIONS /api/team/?department=design HTTP/1.1" 200 0
"GET /api/team/?department=design HTTP/1.1" 200 52
"OPTIONS /api/team/stats/ HTTP/1.1" 200 0
"GET /api/team/stats/ HTTP/1.1" 200 327
"OPTIONS /api/team/?department=media_buying HTTP/1.1" 200 0
"GET /api/team/?department=media_buying HTTP/1.1" 200 52
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/revenue/ HTTP/1.1" 200 1284
"GET /api/revenue/summary/ HTTP/1.1" 200 413
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
Watching for file changes with StatReloader
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/clients/ HTTP/1.1" 200 1116
"GET /api/projects/ HTTP/1.1" 200 1479
"GET /api/projects/ HTTP/1.1" 200 1479
"GET /api/tasks/ HTTP/1.1" 200 1277
"GET /api/tasks/ HTTP/1.1" 200 1277
"GET /api/tasks/ HTTP/1.1" 200 1277
"GET /api/projects/ HTTP/1.1" 200 1479
"OPTIONS /api/reports/?period=current_month HTTP/1.1" 200 0
"GET /api/reports/?period=current_month HTTP/1.1" 200 52
"OPTIONS /api/reports/summary/ HTTP/1.1" 200 0
"GET /api/reports/summary/ HTTP/1.1" 200 104
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/reports/?period=current_month HTTP/1.1" 200 52
"GET /api/reports/summary/ HTTP/1.1" 200 104
"GET /api/reports/?period=current_month HTTP/1.1" 200 52
"GET /api/reports/summary/ HTTP/1.1" 200 104
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/reports/?period=current_month HTTP/1.1" 200 52
"GET /api/reports/summary/ HTTP/1.1" 200 104
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/mtbrmg_erp/settings.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/mtbrmg_erp/settings.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/mtbrmg_erp/settings.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/mtbrmg_erp/settings.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/mtbrmg_erp/urls.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/mtbrmg_erp/settings.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/commissions/models.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/commissions/models.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
Not Found: /api/commissions/
"GET /api/commissions/ HTTP/1.1" 404 36528
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/commissions/urls.py changed, reloading.
Watching for file changes with StatReloader
Unauthorized: /api/commissions/
"GET /api/commissions/ HTTP/1.1" 401 62
"POST /api/auth/login/ HTTP/1.1" 200 950
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"OPTIONS /api/api/commissions/ HTTP/1.1" 200 0
"OPTIONS /api/api/commissions/stats/ HTTP/1.1" 200 0
Not Found: /api/api/commissions/stats/
"GET /api/api/commissions/stats/ HTTP/1.1" 404 43390
Not Found: /api/api/commissions/
"GET /api/api/commissions/ HTTP/1.1" 404 43372
"OPTIONS /api/team/departments/sales/ HTTP/1.1" 200 0
"GET /api/team/departments/sales/ HTTP/1.1" 200 52
"OPTIONS /api/team/departments/sales/stats/ HTTP/1.1" 200 0
"GET /api/team/departments/sales/stats/ HTTP/1.1" 200 293
Not Found: /api/api/commissions/
Not Found: /api/api/commissions/stats/
"GET /api/api/commissions/stats/ HTTP/1.1" 404 43390
"GET /api/api/commissions/ HTTP/1.1" 404 43372
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"OPTIONS /api/commissions/stats/ HTTP/1.1" 200 0
"OPTIONS /api/commissions/ HTTP/1.1" 200 0
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/commissions/ HTTP/1.1" 200 1456
"OPTIONS /api/commissions/1/approve/ HTTP/1.1" 200 0
"POST /api/commissions/1/approve/ HTTP/1.1" 200 1816
"GET /api/commissions/stats/ HTTP/1.1" 200 359
"GET /api/commissions/ HTTP/1.1" 200 1487
"OPTIONS /api/commissions/1/mark_paid/ HTTP/1.1" 200 0
"POST /api/commissions/1/mark_paid/ HTTP/1.1" 200 1821
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/commissions/ HTTP/1.1" 200 1504
"POST /api/auth/login/ HTTP/1.1" 200 950
"GET /api/commissions/ HTTP/1.1" 200 1504
"OPTIONS /api/projects/ HTTP/1.1" 200 0
"OPTIONS /api/tasks/ HTTP/1.1" 200 0
"GET /api/projects/ HTTP/1.1" 200 1946
"GET /api/tasks/ HTTP/1.1" 200 1277
"GET /api/team/departments/sales/ HTTP/1.1" 200 52
"GET /api/team/departments/sales/stats/ HTTP/1.1" 200 293
"OPTIONS /api/team/ HTTP/1.1" 200 0
"OPTIONS /api/team/stats/ HTTP/1.1" 200 0
"GET /api/team/ HTTP/1.1" 200 666
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/team/departments/sales/ HTTP/1.1" 200 52
"GET /api/team/departments/sales/stats/ HTTP/1.1" 200 293
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/commissions/ HTTP/1.1" 200 1504
Watching for file changes with StatReloader
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
Watching for file changes with StatReloader
"POST /api/auth/login/ HTTP/1.1" 200 950
"GET /api/auth/profile/ HTTP/1.1" 200 326
Unauthorized: /api/auth/profile/
"GET /api/auth/profile/ HTTP/1.1" 401 62
Watching for file changes with StatReloader
Unauthorized: /api/auth/profile/
"GET /api/auth/profile/ HTTP/1.1" 401 62
Unauthorized: /api/
"GET /api/ HTTP/1.1" 401 62
Watching for file changes with StatReloader
Unauthorized: /api/
"GET /api/ HTTP/1.1" 401 62
"POST /api/auth/login/ HTTP/1.1" 200 950
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"POST /api/auth/login/ HTTP/1.1" 200 950
"POST /api/auth/login/ HTTP/1.1" 200 950
"POST /api/auth/login/ HTTP/1.1" 200 950
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
"POST /api/auth/logout/ HTTP/1.1" 200 53
"POST /api/auth/login/ HTTP/1.1" 200 950
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
- Broken pipe from ('127.0.0.1', 49179)
- Broken pipe from ('127.0.0.1', 49172)
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"OPTIONS /api/revenue/ HTTP/1.1" 200 0
"GET /api/revenue/ HTTP/1.1" 200 1284
"OPTIONS /api/revenue/summary/ HTTP/1.1" 200 0
"GET /api/revenue/summary/ HTTP/1.1" 200 413
"OPTIONS /api/team/stats/ HTTP/1.1" 200 0
"OPTIONS /api/team/ HTTP/1.1" 200 0
"GET /api/team/ HTTP/1.1" 200 666
"GET /api/team/stats/ HTTP/1.1" 200 327
"OPTIONS /api/projects/ HTTP/1.1" 200 0
"OPTIONS /api/clients/ HTTP/1.1" 200 0
"GET /api/clients/ HTTP/1.1" 200 1116
"GET /api/projects/ HTTP/1.1" 200 1946
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"OPTIONS /api/clients/1/ HTTP/1.1" 200 0
"GET /api/clients/1/ HTTP/1.1" 200 810
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
Watching for file changes with StatReloader
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/clients/1/ HTTP/1.1" 200 810
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"OPTIONS /api/dashboard/overview/ HTTP/1.1" 200 0
"OPTIONS /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 0
"OPTIONS /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 200 0
"OPTIONS /api/clients/?page_size=100 HTTP/1.1" 200 0
"OPTIONS /api/dashboard/overview/ HTTP/1.1" 200 0
"OPTIONS /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 0
"OPTIONS /api/clients/?page_size=100 HTTP/1.1" 200 0
"OPTIONS /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 200 0
"OPTIONS /api/team/?page_size=10 HTTP/1.1" 200 0
"OPTIONS /api/commissions/stats/ HTTP/1.1" 200 0
"OPTIONS /api/team/?page_size=10 HTTP/1.1" 200 0
"OPTIONS /api/commissions/stats/ HTTP/1.1" 200 0
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"OPTIONS /api/clients/3/ HTTP/1.1" 200 0
"GET /api/clients/3/ HTTP/1.1" 200 810
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
Bad Request: /api/tasks/
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"OPTIONS /api/team/departments/sales/ HTTP/1.1" 200 0
"GET /api/team/departments/sales/ HTTP/1.1" 200 52
"OPTIONS /api/team/departments/sales/stats/ HTTP/1.1" 200 0
"GET /api/team/departments/sales/stats/ HTTP/1.1" 200 293
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/revenue/ HTTP/1.1" 200 1284
"GET /api/revenue/summary/ HTTP/1.1" 200 413
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
Bad Request: /api/tasks/
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/revenue/ HTTP/1.1" 200 1284
"GET /api/revenue/summary/ HTTP/1.1" 200 413
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
Bad Request: /api/tasks/
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/clients/ HTTP/1.1" 200 1116
"GET /api/projects/ HTTP/1.1" 200 1946
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Bad Request: /api/tasks/
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
- Broken pipe from ('127.0.0.1', 58071)
Not Found: /
"GET / HTTP/1.1" 404 3808
- Broken pipe from ('127.0.0.1', 58077)
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Unauthorized: /api/
"GET /api/ HTTP/1.1" 401 62
"POST /api/auth/login/ HTTP/1.1" 200 950
Unauthorized: /api/
"GET /api/ HTTP/1.1" 401 62
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"OPTIONS /api/projects/1/ HTTP/1.1" 200 0
"GET /api/projects/1/ HTTP/1.1" 200 1311
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
Bad Request: /api/tasks/
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/departments/sales/ HTTP/1.1" 200 52
"GET /api/team/departments/sales/stats/ HTTP/1.1" 200 293
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
Bad Request: /api/tasks/
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/stats/ HTTP/1.1" 200 327
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
Unauthorized: /api/
"GET /api/ HTTP/1.1" 401 62
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"OPTIONS /api/tasks/1/ HTTP/1.1" 200 0
"GET /api/tasks/1/ HTTP/1.1" 200 1390
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/tasks/1/ HTTP/1.1" 200 1390
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/tasks/1/ HTTP/1.1" 200 1390
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/tasks/1/ HTTP/1.1" 200 1390
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/tasks/1/ HTTP/1.1" 200 1390
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/tasks/1/ HTTP/1.1" 200 1390
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/tasks/1/ HTTP/1.1" 200 1390
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/tasks/1/ HTTP/1.1" 200 1390
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/tasks/1/ HTTP/1.1" 200 1390
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/tasks/1/ HTTP/1.1" 200 1390
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/tasks/1/ HTTP/1.1" 200 1390
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/tasks/1/ HTTP/1.1" 200 1390
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/tasks/1/ HTTP/1.1" 200 1390
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/tasks/1/ HTTP/1.1" 200 1390
"GET /api/tasks/1/ HTTP/1.1" 200 1390
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
Bad Request: /api/tasks/
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/tasks/1/ HTTP/1.1" 200 1390
"GET /api/projects/1/ HTTP/1.1" 200 1311
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/tasks/1/ HTTP/1.1" 200 1390
Bad Request: /api/tasks/
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
Bad Request: /api/tasks/
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/tasks/1/ HTTP/1.1" 200 1390
"GET /api/tasks/1/ HTTP/1.1" 200 1390
"GET /api/projects/ HTTP/1.1" 200 1946
"GET /api/projects/ HTTP/1.1" 200 1946
"POST /api/auth/login/ HTTP/1.1" 200 950
"GET /api/tasks/1/ HTTP/1.1" 200 1390
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/tasks/1/ HTTP/1.1" 200 1390
"GET /api/projects/ HTTP/1.1" 200 1946
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/tasks/1/ HTTP/1.1" 200 1390
"GET /api/projects/ HTTP/1.1" 200 1946
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
Bad Request: /api/tasks/
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/stats/ HTTP/1.1" 200 327
Bad Request: /api/tasks/
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/tasks/1/ HTTP/1.1" 200 1390
"GET /api/projects/ HTTP/1.1" 200 1946
"GET /api/team/ HTTP/1.1" 200 666
Watching for file changes with StatReloader
Unauthorized: /api/commissions/stats/
"GET /api/commissions/stats/ HTTP/1.1" 401 62
Not Found: /api/auth/me/
"GET /api/auth/me/ HTTP/1.1" 404 46580
Unauthorized: /api/clients/
Unauthorized: /api/projects/
Unauthorized: /api/dashboard/overview/
"GET /api/clients/?page_size=100 HTTP/1.1" 401 62
Unauthorized: /api/team/
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 401 62
"GET /api/dashboard/overview/ HTTP/1.1" 401 62
Unauthorized: /api/tasks/
Unauthorized: /api/team/stats/
"GET /api/team/?page_size=10 HTTP/1.1" 401 62
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 401 62
"GET /api/team/stats/ HTTP/1.1" 401 62
Bad Request: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 400 46
"POST /api/auth/login/ HTTP/1.1" 200 1212
Unauthorized: /api/dashboard/overview/
Unauthorized: /api/projects/
Unauthorized: /api/clients/
"GET /api/dashboard/overview/ HTTP/1.1" 401 62
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 401 62
"GET /api/clients/?page_size=100 HTTP/1.1" 401 62
Unauthorized: /api/tasks/
Unauthorized: /api/team/
Unauthorized: /api/team/stats/
Unauthorized: /api/commissions/stats/
"GET /api/team/stats/ HTTP/1.1" 401 62
"GET /api/team/?page_size=10 HTTP/1.1" 401 62
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 401 62
"GET /api/commissions/stats/ HTTP/1.1" 401 62
"POST /api/auth/login/ HTTP/1.1" 200 950
"GET /api/auth/profile/ HTTP/1.1" 200 326
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"POST /api/auth/login/ HTTP/1.1" 200 950
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/tasks/1/ HTTP/1.1" 200 1390
"GET /api/projects/ HTTP/1.1" 200 1946
"OPTIONS /api/projects/3/ HTTP/1.1" 200 0
"GET /api/projects/3/ HTTP/1.1" 200 1299
Bad Request: /api/tasks/
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/projects/1/ HTTP/1.1" 200 1311
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/projects/1/ HTTP/1.1" 200 1311
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
Bad Request: /api/tasks/
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
Bad Request: /api/tasks/
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/revenue/ HTTP/1.1" 200 1284
"GET /api/revenue/summary/ HTTP/1.1" 200 413
Bad Request: /api/tasks/
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
Bad Request: /api/tasks/
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/clients/ HTTP/1.1" 200 1116
"GET /api/projects/ HTTP/1.1" 200 1946
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/stats/ HTTP/1.1" 200 327
Bad Request: /api/tasks/
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Watching for file changes with StatReloader
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"OPTIONS /api/dashboard/overview/ HTTP/1.1" 200 0
"OPTIONS /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 0
"OPTIONS /api/clients/?page_size=100 HTTP/1.1" 200 0
"OPTIONS /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 200 0
"OPTIONS /api/team/?page_size=10 HTTP/1.1" 200 0
"OPTIONS /api/team/stats/ HTTP/1.1" 200 0
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
Bad Request: /api/tasks/
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Watching for file changes with StatReloader
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"OPTIONS /api/team/ HTTP/1.1" 200 0
"OPTIONS /api/team/stats/ HTTP/1.1" 200 0
"GET /api/team/ HTTP/1.1" 200 666
"GET /api/team/stats/ HTTP/1.1" 200 327
"OPTIONS /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 0
"OPTIONS /api/dashboard/overview/ HTTP/1.1" 200 0
"OPTIONS /api/clients/?page_size=100 HTTP/1.1" 200 0
"OPTIONS /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 200 0
"OPTIONS /api/commissions/stats/ HTTP/1.1" 200 0
"OPTIONS /api/team/?page_size=10 HTTP/1.1" 200 0
"OPTIONS /api/dashboard/overview/ HTTP/1.1" 200 0
"OPTIONS /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 0
"OPTIONS /api/clients/?page_size=100 HTTP/1.1" 200 0
"OPTIONS /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 200 0
"OPTIONS /api/commissions/stats/ HTTP/1.1" 200 0
"OPTIONS /api/team/?page_size=10 HTTP/1.1" 200 0
Bad Request: /api/tasks/
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
Bad Request: /api/tasks/
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
"OPTIONS /api/team/departments/sales/ HTTP/1.1" 200 0
"GET /api/team/departments/sales/ HTTP/1.1" 200 52
"OPTIONS /api/team/departments/sales/stats/ HTTP/1.1" 200 0
"GET /api/team/departments/sales/stats/ HTTP/1.1" 200 293
"OPTIONS /api/commissions/ HTTP/1.1" 200 0
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/commissions/ HTTP/1.1" 200 1504
"GET /api/team/departments/sales/ HTTP/1.1" 200 52
"GET /api/team/departments/sales/stats/ HTTP/1.1" 200 293
"GET /api/team/ HTTP/1.1" 200 666
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
Bad Request: /api/tasks/
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/projects/ HTTP/1.1" 200 1946
"GET /api/tasks/1/ HTTP/1.1" 200 1390
"GET /api/projects/ HTTP/1.1" 200 1946
"GET /api/tasks/1/ HTTP/1.1" 200 1390
"GET /api/projects/ HTTP/1.1" 200 1946
"GET /api/tasks/1/ HTTP/1.1" 200 1390
"GET /api/projects/ HTTP/1.1" 200 1946
"GET /api/tasks/1/ HTTP/1.1" 200 1390
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"POST /api/auth/login/ HTTP/1.1" 200 950
"OPTIONS /api/dashboard/overview/ HTTP/1.1" 200 0
"OPTIONS /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 0
"OPTIONS /api/team/?page_size=10 HTTP/1.1" 200 0
"OPTIONS /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 0
"OPTIONS /api/dashboard/overview/ HTTP/1.1" 200 0
"OPTIONS /api/clients/?page_size=100 HTTP/1.1" 200 0
"OPTIONS /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 200 0
"OPTIONS /api/commissions/stats/ HTTP/1.1" 200 0
"OPTIONS /api/team/?page_size=10 HTTP/1.1" 200 0
"OPTIONS /api/team/stats/ HTTP/1.1" 200 0
"OPTIONS /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 200 0
"OPTIONS /api/clients/?page_size=100 HTTP/1.1" 200 0
"OPTIONS /api/commissions/stats/ HTTP/1.1" 200 0
"OPTIONS /api/team/stats/ HTTP/1.1" 200 0
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/team/stats/ HTTP/1.1" 200 327
"OPTIONS /api/clients/1/ HTTP/1.1" 200 0
"GET /api/clients/1/ HTTP/1.1" 200 810
"GET /api/clients/1/ HTTP/1.1" 200 810
"GET /api/clients/1/ HTTP/1.1" 200 810
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
"OPTIONS /api/dashboard/overview/ HTTP/1.1" 200 0
"OPTIONS /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 0
"OPTIONS /api/clients/?page_size=100 HTTP/1.1" 200 0
"OPTIONS /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 200 0
"OPTIONS /api/team/?page_size=10 HTTP/1.1" 200 0
"OPTIONS /api/commissions/stats/ HTTP/1.1" 200 0
"OPTIONS /api/team/stats/ HTTP/1.1" 200 0
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Bad Request: /api/tasks/
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Bad Request: /api/tasks/
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Watching for file changes with StatReloader
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"OPTIONS /api/dashboard/overview/ HTTP/1.1" 200 0
"OPTIONS /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 200 0
"OPTIONS /api/clients/?page_size=100 HTTP/1.1" 200 0
"OPTIONS /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 0
"OPTIONS /api/commissions/stats/ HTTP/1.1" 200 0
"OPTIONS /api/team/?page_size=10 HTTP/1.1" 200 0
"OPTIONS /api/clients/?page_size=100 HTTP/1.1" 200 0
"OPTIONS /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 0
"OPTIONS /api/dashboard/overview/ HTTP/1.1" 200 0
"OPTIONS /api/team/stats/ HTTP/1.1" 200 0
"OPTIONS /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 200 0
"OPTIONS /api/team/?page_size=10 HTTP/1.1" 200 0
"OPTIONS /api/commissions/stats/ HTTP/1.1" 200 0
"OPTIONS /api/team/stats/ HTTP/1.1" 200 0
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Bad Request: /api/tasks/
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"OPTIONS /api/projects/ HTTP/1.1" 200 0
"OPTIONS /api/clients/ HTTP/1.1" 200 0
"GET /api/clients/ HTTP/1.1" 200 1116
"GET /api/projects/ HTTP/1.1" 200 1946
"OPTIONS /api/tasks/ HTTP/1.1" 200 0
"GET /api/projects/ HTTP/1.1" 200 1946
"GET /api/tasks/ HTTP/1.1" 200 1277
"OPTIONS /api/team/departments/sales/ HTTP/1.1" 200 0
"GET /api/team/departments/sales/ HTTP/1.1" 200 52
"OPTIONS /api/team/departments/sales/stats/ HTTP/1.1" 200 0
"GET /api/team/departments/sales/stats/ HTTP/1.1" 200 293
"OPTIONS /api/team/departments/development/ HTTP/1.1" 200 0
"GET /api/team/departments/development/ HTTP/1.1" 200 52
"OPTIONS /api/team/departments/development/stats/ HTTP/1.1" 200 0
"GET /api/team/departments/development/stats/ HTTP/1.1" 200 291
"OPTIONS /api/expenses/ HTTP/1.1" 200 0
"GET /api/expenses/ HTTP/1.1" 200 1025
"OPTIONS /api/expenses/summary/ HTTP/1.1" 200 0
"GET /api/expenses/summary/ HTTP/1.1" 200 545
"OPTIONS /api/expense-categories/ HTTP/1.1" 200 0
"GET /api/expense-categories/ HTTP/1.1" 200 970
"OPTIONS /api/revenue/ HTTP/1.1" 200 0
"GET /api/revenue/ HTTP/1.1" 200 1284
"OPTIONS /api/revenue/summary/ HTTP/1.1" 200 0
"GET /api/revenue/summary/ HTTP/1.1" 200 413
"GET /api/expenses/ HTTP/1.1" 200 1025
"GET /api/expenses/summary/ HTTP/1.1" 200 545
"GET /api/expense-categories/ HTTP/1.1" 200 970
"OPTIONS /api/cash-flow/?period=current_year&type=monthly HTTP/1.1" 200 0
"GET /api/cash-flow/?period=current_year&type=monthly HTTP/1.1" 200 1589
"OPTIONS /api/cash-flow/summary/ HTTP/1.1" 200 0
"GET /api/cash-flow/summary/ HTTP/1.1" 200 766
"OPTIONS /api/reports/?period=current_month HTTP/1.1" 200 0
"GET /api/reports/?period=current_month HTTP/1.1" 200 52
"OPTIONS /api/reports/summary/ HTTP/1.1" 200 0
"GET /api/reports/summary/ HTTP/1.1" 200 104
Unauthorized: /api/
"GET /api/ HTTP/1.1" 401 62
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"OPTIONS /api/revenue/ HTTP/1.1" 200 0
"GET /api/revenue/ HTTP/1.1" 200 1284
"OPTIONS /api/revenue/summary/ HTTP/1.1" 200 0
"GET /api/revenue/summary/ HTTP/1.1" 200 413
"OPTIONS /api/expenses/ HTTP/1.1" 200 0
"GET /api/expenses/ HTTP/1.1" 200 1025
"OPTIONS /api/expenses/summary/ HTTP/1.1" 200 0
"GET /api/expenses/summary/ HTTP/1.1" 200 545
"OPTIONS /api/expense-categories/ HTTP/1.1" 200 0
"GET /api/expense-categories/ HTTP/1.1" 200 970
"OPTIONS /api/cash-flow/?period=current_year&type=monthly HTTP/1.1" 200 0
"GET /api/cash-flow/?period=current_year&type=monthly HTTP/1.1" 200 1589
"OPTIONS /api/cash-flow/summary/ HTTP/1.1" 200 0
"GET /api/cash-flow/summary/ HTTP/1.1" 200 766
"OPTIONS /api/reports/?period=current_month HTTP/1.1" 200 0
"GET /api/reports/?period=current_month HTTP/1.1" 200 52
"OPTIONS /api/reports/summary/ HTTP/1.1" 200 0
"GET /api/reports/summary/ HTTP/1.1" 200 104
"OPTIONS /api/budgets/?year=2025 HTTP/1.1" 200 0
"GET /api/budgets/?year=2025 HTTP/1.1" 200 1292
"OPTIONS /api/budgets/summary/ HTTP/1.1" 200 0
"GET /api/budgets/summary/ HTTP/1.1" 200 1625
"OPTIONS /api/dashboard/overview/ HTTP/1.1" 200 0
"OPTIONS /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 0
"OPTIONS /api/clients/?page_size=100 HTTP/1.1" 200 0
"OPTIONS /api/commissions/stats/ HTTP/1.1" 200 0
"OPTIONS /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 200 0
"OPTIONS /api/team/?page_size=10 HTTP/1.1" 200 0
"OPTIONS /api/team/stats/ HTTP/1.1" 200 0
Bad Request: /api/tasks/
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
Bad Request: /api/tasks/
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"OPTIONS /api/revenue/ HTTP/1.1" 200 0
"GET /api/revenue/ HTTP/1.1" 200 1284
"OPTIONS /api/revenue/summary/ HTTP/1.1" 200 0
"GET /api/revenue/summary/ HTTP/1.1" 200 413
"OPTIONS /api/expenses/ HTTP/1.1" 200 0
"GET /api/expenses/ HTTP/1.1" 200 1025
"OPTIONS /api/expenses/summary/ HTTP/1.1" 200 0
"GET /api/expenses/summary/ HTTP/1.1" 200 545
"OPTIONS /api/expense-categories/ HTTP/1.1" 200 0
"GET /api/expense-categories/ HTTP/1.1" 200 970
"OPTIONS /api/cash-flow/?period=current_year&type=monthly HTTP/1.1" 200 0
"GET /api/cash-flow/?period=current_year&type=monthly HTTP/1.1" 200 1589
"OPTIONS /api/cash-flow/summary/ HTTP/1.1" 200 0
"GET /api/cash-flow/summary/ HTTP/1.1" 200 766
"OPTIONS /api/reports/?period=current_month HTTP/1.1" 200 0
"GET /api/reports/?period=current_month HTTP/1.1" 200 52
"OPTIONS /api/reports/summary/ HTTP/1.1" 200 0
"GET /api/reports/summary/ HTTP/1.1" 200 104
"OPTIONS /api/budgets/?year=2025 HTTP/1.1" 200 0
"GET /api/budgets/?year=2025 HTTP/1.1" 200 1292
"OPTIONS /api/budgets/summary/ HTTP/1.1" 200 0
"GET /api/budgets/summary/ HTTP/1.1" 200 1625
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
Bad Request: /api/tasks/
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"OPTIONS /api/dashboard/overview/ HTTP/1.1" 200 0
"OPTIONS /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 0
"OPTIONS /api/clients/?page_size=100 HTTP/1.1" 200 0
"OPTIONS /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 200 0
"OPTIONS /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 0
"OPTIONS /api/dashboard/overview/ HTTP/1.1" 200 0
"OPTIONS /api/commissions/stats/ HTTP/1.1" 200 0
"OPTIONS /api/team/?page_size=10 HTTP/1.1" 200 0
"OPTIONS /api/clients/?page_size=100 HTTP/1.1" 200 0
"OPTIONS /api/team/stats/ HTTP/1.1" 200 0
"OPTIONS /api/commissions/stats/ HTTP/1.1" 200 0
"OPTIONS /api/team/?page_size=10 HTTP/1.1" 200 0
"OPTIONS /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 200 0
"OPTIONS /api/team/stats/ HTTP/1.1" 200 0
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Bad Request: /api/tasks/
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
"OPTIONS /api/projects/ HTTP/1.1" 200 0
"OPTIONS /api/clients/ HTTP/1.1" 200 0
"GET /api/clients/ HTTP/1.1" 200 1116
"GET /api/projects/ HTTP/1.1" 200 1946
"OPTIONS /api/tasks/ HTTP/1.1" 200 0
"GET /api/projects/ HTTP/1.1" 200 1946
"GET /api/tasks/ HTTP/1.1" 200 1277
"GET /api/clients/ HTTP/1.1" 200 1116
"GET /api/projects/ HTTP/1.1" 200 1946
"GET /api/clients/ HTTP/1.1" 200 1116
"GET /api/clients/ HTTP/1.1" 200 1116
"OPTIONS /api/team/ HTTP/1.1" 200 0
"GET /api/team/ HTTP/1.1" 200 666
"OPTIONS /api/team/departments/sales/ HTTP/1.1" 200 0
"GET /api/team/departments/sales/ HTTP/1.1" 200 52
"OPTIONS /api/team/departments/sales/stats/ HTTP/1.1" 200 0
"GET /api/team/departments/sales/stats/ HTTP/1.1" 200 293
"OPTIONS /api/commissions/ HTTP/1.1" 200 0
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/commissions/ HTTP/1.1" 200 1504
"OPTIONS /api/team/departments/development/ HTTP/1.1" 200 0
"GET /api/team/departments/development/ HTTP/1.1" 200 52
"OPTIONS /api/team/departments/development/stats/ HTTP/1.1" 200 0
"GET /api/team/departments/development/stats/ HTTP/1.1" 200 291
"GET /api/team/ HTTP/1.1" 200 666
"GET /api/team/stats/ HTTP/1.1" 200 327
Bad Request: /api/tasks/
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
Bad Request: /api/tasks/
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/stats/ HTTP/1.1" 200 327
Bad Request: /api/tasks/
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/clients/ HTTP/1.1" 200 1116
"GET /api/projects/ HTTP/1.1" 200 1946
"OPTIONS /api/projects/4/ HTTP/1.1" 200 0
"GET /api/projects/4/ HTTP/1.1" 200 1290
"GET /api/clients/ HTTP/1.1" 200 1116
"GET /api/projects/ HTTP/1.1" 200 1946
"GET /api/team/departments/sales/ HTTP/1.1" 200 52
"GET /api/team/departments/sales/stats/ HTTP/1.1" 200 293
"GET /api/team/departments/development/ HTTP/1.1" 200 52
"GET /api/team/departments/development/stats/ HTTP/1.1" 200 291
"OPTIONS /api/team/?department=design HTTP/1.1" 200 0
"GET /api/team/?department=design HTTP/1.1" 200 52
"GET /api/team/stats/ HTTP/1.1" 200 327
"OPTIONS /api/team/?department=media_buying HTTP/1.1" 200 0
"GET /api/team/?department=media_buying HTTP/1.1" 200 52
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/team/?department=media_buying HTTP/1.1" 200 52
"GET /api/team/stats/ HTTP/1.1" 200 327
"OPTIONS /api/revenue/ HTTP/1.1" 200 0
"GET /api/revenue/ HTTP/1.1" 200 1284
"OPTIONS /api/revenue/summary/ HTTP/1.1" 200 0
"GET /api/revenue/summary/ HTTP/1.1" 200 413
"OPTIONS /api/expenses/ HTTP/1.1" 200 0
"GET /api/expenses/ HTTP/1.1" 200 1025
"OPTIONS /api/expenses/summary/ HTTP/1.1" 200 0
"GET /api/expenses/summary/ HTTP/1.1" 200 545
"OPTIONS /api/expense-categories/ HTTP/1.1" 200 0
"GET /api/expense-categories/ HTTP/1.1" 200 970
Bad Request: /api/tasks/
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/stats/ HTTP/1.1" 200 327
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
Bad Request: /api/tasks/
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"OPTIONS /api/clients/1/ HTTP/1.1" 200 0
"GET /api/clients/1/ HTTP/1.1" 200 810
"GET /api/clients/1/ HTTP/1.1" 200 810
"GET /api/clients/ HTTP/1.1" 200 1116
"GET /api/projects/ HTTP/1.1" 200 1946
"GET /api/clients/ HTTP/1.1" 200 1116
"GET /api/clients/ HTTP/1.1" 200 1116
"GET /api/projects/ HTTP/1.1" 200 1946
"GET /api/tasks/ HTTP/1.1" 200 1277
"GET /api/team/ HTTP/1.1" 200 666
"GET /api/team/departments/sales/ HTTP/1.1" 200 52
"GET /api/team/departments/sales/stats/ HTTP/1.1" 200 293
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/tasks/ HTTP/1.1" 200 1277
"GET /api/projects/ HTTP/1.1" 200 1946
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/clients/1/ HTTP/1.1" 200 810
"GET /api/clients/ HTTP/1.1" 200 1116
"GET /api/projects/ HTTP/1.1" 200 1946
"GET /api/team/departments/sales/ HTTP/1.1" 200 52
"GET /api/team/departments/sales/stats/ HTTP/1.1" 200 293
"GET /api/team/?department=media_buying HTTP/1.1" 200 52
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/team/?department=media_buying HTTP/1.1" 200 52
"GET /api/team/stats/ HTTP/1.1" 200 327
Bad Request: /api/tasks/
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/team/?department=media_buying HTTP/1.1" 200 52
"GET /api/team/stats/ HTTP/1.1" 200 327
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Bad Request: /api/tasks/
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/stats/ HTTP/1.1" 200 327
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Not Found: /
"GET / HTTP/1.1" 404 3808
Watching for file changes with StatReloader
Unauthorized: /api/
"GET /api/ HTTP/1.1" 401 62
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/clients/ HTTP/1.1" 200 1116
"GET /api/projects/ HTTP/1.1" 200 1946
"GET /api/projects/ HTTP/1.1" 200 1946
"GET /api/tasks/ HTTP/1.1" 200 1277
"GET /api/team/departments/sales/ HTTP/1.1" 200 52
"GET /api/team/departments/sales/stats/ HTTP/1.1" 200 293
"GET /api/team/departments/development/ HTTP/1.1" 200 52
"GET /api/team/departments/development/stats/ HTTP/1.1" 200 291
"GET /api/team/?department=design HTTP/1.1" 200 52
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/team/?department=media_buying HTTP/1.1" 200 52
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/revenue/ HTTP/1.1" 200 1284
"GET /api/revenue/summary/ HTTP/1.1" 200 413
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/clients/ HTTP/1.1" 200 1116
"GET /api/projects/ HTTP/1.1" 200 1946
"GET /api/projects/ HTTP/1.1" 200 1946
"GET /api/tasks/ HTTP/1.1" 200 1277
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"OPTIONS /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 0
"OPTIONS /api/clients/?page_size=100 HTTP/1.1" 200 0
"OPTIONS /api/dashboard/overview/ HTTP/1.1" 200 0
"OPTIONS /api/team/?page_size=10 HTTP/1.1" 200 0
"OPTIONS /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 200 0
"OPTIONS /api/team/stats/ HTTP/1.1" 200 0
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Bad Request: /api/tasks/
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"OPTIONS /api/commissions/stats/ HTTP/1.1" 200 0
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Bad Request: /api/tasks/
- Broken pipe from ('127.0.0.1', 56560)
- Broken pipe from ('127.0.0.1', 56561)
- Broken pipe from ('127.0.0.1', 56562)
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
- Broken pipe from ('127.0.0.1', 56559)
"GET /api/commissions/stats/ HTTP/1.1" 200 362
- Broken pipe from ('127.0.0.1', 56674)
"GET /api/team/stats/ HTTP/1.1" 200 327
- Broken pipe from ('127.0.0.1', 56675)
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
- Broken pipe from ('127.0.0.1', 56934)
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
- Broken pipe from ('127.0.0.1', 56935)
"GET /api/team/stats/ HTTP/1.1" 200 327
- Broken pipe from ('127.0.0.1', 56936)
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Bad Request: /api/tasks/
- Broken pipe from ('127.0.0.1', 57172)
- Broken pipe from ('127.0.0.1', 57173)
- Broken pipe from ('127.0.0.1', 57175)
- Broken pipe from ('127.0.0.1', 57174)
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
- Broken pipe from ('127.0.0.1', 57170)
- Broken pipe from ('127.0.0.1', 57171)
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
Watching for file changes with StatReloader
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Bad Request: /api/tasks/
- Broken pipe from ('127.0.0.1', 57371)
"GET /api/clients/?page_size=100 HTTP/1.1" 200 1116
- Broken pipe from ('127.0.0.1', 57370)
- Broken pipe from ('127.0.0.1', 57372)
- Broken pipe from ('127.0.0.1', 57369)
- Broken pipe from ('127.0.0.1', 57373)
- Broken pipe from ('127.0.0.1', 57368)
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Not Found: /api/quotations/
Not Found: /api/quotations/stats/
"GET /api/quotations/ HTTP/1.1" 404 43357
"GET /api/quotations/stats/ HTTP/1.1" 404 43375
- Broken pipe from ('127.0.0.1', 57605)
- Broken pipe from ('127.0.0.1', 57606)
Not Found: /api/quotations/
Not Found: /api/quotations/stats/
"GET /api/quotations/ HTTP/1.1" 404 43357
"GET /api/quotations/stats/ HTTP/1.1" 404 43375
- Broken pipe from ('127.0.0.1', 57611)
- Broken pipe from ('127.0.0.1', 57612)
Not Found: /api/quotations/
"GET /api/quotations/ HTTP/1.1" 404 43357
- Broken pipe from ('127.0.0.1', 57614)
Not Found: /api/quotations/stats/
"GET /api/quotations/stats/ HTTP/1.1" 404 43375
- Broken pipe from ('127.0.0.1', 57616)
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"OPTIONS /api/dashboard/overview/ HTTP/1.1" 200 0
"OPTIONS /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 0
"OPTIONS /api/clients/?page_size=100 HTTP/1.1" 200 0
"OPTIONS /api/team/stats/ HTTP/1.1" 200 0
"OPTIONS /api/team/?page_size=10 HTTP/1.1" 200 0
"OPTIONS /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 200 0
"OPTIONS /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 0
"OPTIONS /api/clients/?page_size=100 HTTP/1.1" 200 0
"OPTIONS /api/team/stats/ HTTP/1.1" 200 0
"OPTIONS /api/dashboard/overview/ HTTP/1.1" 200 0
"OPTIONS /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 200 0
"OPTIONS /api/team/?page_size=10 HTTP/1.1" 200 0
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/clients/?page_size=100 HTTP/1.1" 200 2183
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/clients/?page_size=100 HTTP/1.1" 200 2183
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/stats/ HTTP/1.1" 200 327
Not Found: /api/quotations/stats/
Not Found: /api/quotations/
"GET /api/quotations/ HTTP/1.1" 404 43357
- Broken pipe from ('127.0.0.1', 57717)
"GET /api/quotations/stats/ HTTP/1.1" 404 43375
- Broken pipe from ('127.0.0.1', 57718)
Not Found: /api/quotations/
Not Found: /api/quotations/stats/
"GET /api/quotations/ HTTP/1.1" 404 43357
- Broken pipe from ('127.0.0.1', 57721)
"GET /api/quotations/stats/ HTTP/1.1" 404 43375
- Broken pipe from ('127.0.0.1', 57722)
Not Found: /api/quotations/
Not Found: /api/quotations/stats/
"GET /api/quotations/ HTTP/1.1" 404 43357
"GET /api/quotations/stats/ HTTP/1.1" 404 43375
- Broken pipe from ('127.0.0.1', 57724)
- Broken pipe from ('127.0.0.1', 57726)
Bad Request: /api/tasks/
"GET /api/clients/?page_size=100 HTTP/1.1" 200 2183
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/clients/?page_size=100 HTTP/1.1" 200 2183
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 2183
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/clients/?page_size=100 HTTP/1.1" 200 2183
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/clients/?page_size=100 HTTP/1.1" 200 2183
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 2183
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Bad Request: /api/tasks/
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"OPTIONS /api/quotations/stats/ HTTP/1.1" 200 0
"OPTIONS /api/quotations/ HTTP/1.1" 200 0
"OPTIONS /api/quotations/stats/ HTTP/1.1" 200 0
"OPTIONS /api/quotations/ HTTP/1.1" 200 0
Not Found: /api/quotations/
"GET /api/quotations/ HTTP/1.1" 404 43357
Not Found: /api/quotations/stats/
"GET /api/quotations/stats/ HTTP/1.1" 404 43375
Not Found: /api/quotations/
Not Found: /api/quotations/stats/
"GET /api/quotations/ HTTP/1.1" 404 43357
"GET /api/quotations/stats/ HTTP/1.1" 404 43375
"OPTIONS /api/service-catalog/?is_active=true HTTP/1.1" 200 0
"OPTIONS /api/service-catalog/?is_active=true HTTP/1.1" 200 0
"OPTIONS /api/clients/ HTTP/1.1" 200 0
"OPTIONS /api/clients/ HTTP/1.1" 200 0
Not Found: /api/service-catalog/
"GET /api/service-catalog/?is_active=true HTTP/1.1" 404 43387
Not Found: /api/service-catalog/
"GET /api/service-catalog/?is_active=true HTTP/1.1" 404 43387
"GET /api/clients/ HTTP/1.1" 200 2183
"GET /api/clients/ HTTP/1.1" 200 2183
Not Found: /api/quotations/stats/
Not Found: /api/quotations/
"GET /api/quotations/ HTTP/1.1" 404 43357
"GET /api/quotations/stats/ HTTP/1.1" 404 43375
Not Found: /api/quotations/
Not Found: /api/quotations/stats/
"GET /api/quotations/ HTTP/1.1" 404 43357
"GET /api/quotations/stats/ HTTP/1.1" 404 43375
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
Not Found: /api/quotations/stats/
Not Found: /api/quotations/
"GET /api/quotations/stats/ HTTP/1.1" 404 43375
"GET /api/quotations/ HTTP/1.1" 404 43357
Not Found: /api/quotations/
"GET /api/quotations/ HTTP/1.1" 404 43357
Not Found: /api/quotations/stats/
"GET /api/quotations/stats/ HTTP/1.1" 404 43375
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
Not Found: /api/quotations/
"GET /api/quotations/ HTTP/1.1" 404 43357
Not Found: /api/quotations/stats/
"GET /api/quotations/stats/ HTTP/1.1" 404 43375
Not Found: /api/quotations/
"GET /api/quotations/ HTTP/1.1" 404 43357
Not Found: /api/quotations/stats/
"GET /api/quotations/stats/ HTTP/1.1" 404 43375
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"GET /api/auth/profile/ HTTP/1.1" 200 326
"OPTIONS /api/quotations/ HTTP/1.1" 200 0
"OPTIONS /api/quotations/stats/ HTTP/1.1" 200 0
"OPTIONS /api/quotations/ HTTP/1.1" 200 0
"OPTIONS /api/quotations/stats/ HTTP/1.1" 200 0
Not Found: /api/quotations/
Not Found: /api/quotations/
"GET /api/quotations/ HTTP/1.1" 404 43357
Not Found: /api/quotations/stats/
Not Found: /api/quotations/stats/
"GET /api/quotations/stats/ HTTP/1.1" 404 43375
"GET /api/quotations/ HTTP/1.1" 404 43357
"GET /api/quotations/stats/ HTTP/1.1" 404 43375
Bad Request: /api/tasks/
"GET /api/clients/?page_size=100 HTTP/1.1" 200 2183
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/clients/?page_size=100 HTTP/1.1" 200 2183
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
Bad Request: /api/tasks/
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Bad Request: /api/tasks/
"GET /api/clients/?page_size=100 HTTP/1.1" 200 2183
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/commissions/stats/ HTTP/1.1" 200 362
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Bad Request: /api/tasks/
"GET /api/clients/?page_size=100 HTTP/1.1" 200 2183
"GET /api/tasks/?priority=high,urgent&page_size=10 HTTP/1.1" 400 111
"OPTIONS /api/commissions/stats/ HTTP/1.1" 200 0
"GET /api/team/?page_size=10 HTTP/1.1" 200 666
"GET /api/projects/?page_size=10&ordering=-created_at HTTP/1.1" 200 1946
"GET /api/team/stats/ HTTP/1.1" 200 327
"GET /api/dashboard/overview/ HTTP/1.1" 200 345
"GET /api/commissions/stats/ HTTP/1.1" 200 362
Watching for file changes with StatReloader
Watching for file changes with StatReloader
